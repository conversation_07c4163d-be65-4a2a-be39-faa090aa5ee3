# Copyright (C) 2022 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

HOST_LIB_NAME := app_manifest
KERNEL_MANIFEST_DIR := kernel/rctee/lib/app_manifest

LOCAL_DIR := $(GET_LOCAL_DIR)


HOST_LIB_SRCS := \
	$(KERNEL_MANIFEST_DIR)/app_manifest.c \

HOST_INCLUDE_DIRS += \
	$(KERNEL_MANIFEST_DIR)/include/ \

include make/host_lib.mk
