# Trusty TEE 可信存储新设计方案

## 1. 概述

### 1.0 GP TEE 常量定义

```c
/* GP TEE 对象类型定义 */
#define TEE_TYPE_AES                    0xA0000010
#define TEE_TYPE_DES                    0xA0000011
#define TEE_TYPE_DES3                   0xA0000013
#define TEE_TYPE_HMAC_MD5               0xA0000001
#define TEE_TYPE_HMAC_SHA1              0xA0000002
#define TEE_TYPE_HMAC_SHA224            0xA0000003
#define TEE_TYPE_HMAC_SHA256            0xA0000004
#define TEE_TYPE_HMAC_SHA384            0xA0000005
#define TEE_TYPE_HMAC_SHA512            0xA0000006
#define TEE_TYPE_RSA_PUBLIC_KEY         0xA0000030
#define TEE_TYPE_RSA_KEYPAIR            0xA1000030
#define TEE_TYPE_DSA_PUBLIC_KEY         0xA0000031
#define TEE_TYPE_DSA_KEYPAIR            0xA1000031
#define TEE_TYPE_DH_KEYPAIR             0xA1000032
#define TEE_TYPE_ECDSA_PUBLIC_KEY       0xA0000041
#define TEE_TYPE_ECDSA_KEYPAIR          0xA1000041
#define TEE_TYPE_ECDH_PUBLIC_KEY        0xA0000042
#define TEE_TYPE_ECDH_KEYPAIR           0xA1000042
#define TEE_TYPE_GENERIC_SECRET         0xA0000000

/* GP TEE 属性标识符定义 */
#define TEE_ATTR_SECRET_VALUE           0xC0000000
#define TEE_ATTR_RSA_MODULUS            0xD0000130
#define TEE_ATTR_FLAG_VALUE             0x20000000
#define TEE_ATTR_FLAG_SET               0x1

/* GP TEE 存储标识符定义 */
#define TEE_STORAGE_PRIVATE             0x00000001
#define TEE_STORAGE_PRIVATE_REE         0x80000000

/* GP TEE 数据访问标志定义 */
#define TEE_DATA_FLAG_ACCESS_READ       0x00000001
#define TEE_DATA_FLAG_ACCESS_WRITE      0x00000002
#define TEE_DATA_FLAG_ACCESS_WRITE_META 0x00000004
#define TEE_DATA_FLAG_SHARE_READ        0x00000010
#define TEE_DATA_FLAG_SHARE_WRITE       0x00000020
#define TEE_DATA_FLAG_CREATE            0x00000200
#define TEE_DATA_FLAG_EXCLUSIVE         0x00000400

/* GP TEE 对象使用标志定义 */
#define TEE_USAGE_EXTRACTABLE           0x00000001
#define TEE_USAGE_ENCRYPT               0x00000002
#define TEE_USAGE_DECRYPT               0x00000004
#define TEE_USAGE_MAC                   0x00000008
#define TEE_USAGE_SIGN                  0x00000010
#define TEE_USAGE_VERIFY                0x00000020
#define TEE_USAGE_DERIVE                0x00000040

/* GP TEE 对象 ID 最大长度 */
#define TEE_OBJECT_ID_MAX_LEN           64

/* Trusty 特有常量 */
#define INVALID_IPC_HANDLE              (-1)
#define STORAGE_TA_PORT                 "com.android.trusty.storage"
#define MAX_OBJECTS_PER_TA              256
```

### 1.1 设计目标

本文档详细描述 Trusty TEE 项目中可信存储（trusted storage）的新设计方案，基于 OP-TEE 成熟的双层对象模型，在 Trusty 用户空间环境中实现 GP 标准的可信存储功能。

**核心设计目标：**
- **OP-TEE 架构适配**：完全基于 OP-TEE 的 tee_obj + tee_pobj 双层对象模型
- **用户空间实现**：所有对象管理在用户空间完成，避免内核修改
- **简化设计原则**：保持方案简洁，避免过度复杂化
- **GP 标准兼容**：上层提供完整的 GP 存储 API 接口

### 1.2 适用范围和限制

**适用范围：**
- 支持 GP 标准的瞬时对象（Transient Object）和持久化对象（Persistent Object）
- 适用于单实例 TA 环境下的存储需求
- 支持完整的 GP 存储 API 接口

**设计限制：**
- 多实例支持将在后续版本中单独设计，本文档暂不涉及
- panic 流程将在后续版本中单独设计，本文档暂不涉及
- 当前设计主要针对功能实现，性能优化将在后续迭代中完善

### 1.3 整体架构

基于 OP-TEE 设计原理，采用双层对象模型：

```mermaid
graph TB
    subgraph "GP API Layer"
        A1[TEE_OpenPersistentObject]
        A2[TEE_CreatePersistentObject]
        A3[TEE_AllocateTransientObject]
        A4[TEE_ReadObjectData]
        A5[TEE_WriteObjectData]
    end

    subgraph "TA 用户空间 - tee_obj 管理"
        B1[struct trusty_tee_obj]
        B2[libutee 库管理]
        B3[TA 对象链表]
    end

    subgraph "存储 TA - 简化 tee_pobj 管理"
        C1[struct trusty_pobj<br/>包含 TA 标识字段]
        C2[单链表全局管理]
        C3[直接存储操作]
    end

    subgraph "Trusty 存储服务"
        D1[storage_open_file]
        D2[storage_read/write]
        D3[storage_delete_file]
    end

    A1 --> B1
    A2 --> B1
    A3 --> B1
    A4 --> B1
    A5 --> B1

    B1 --> C1
    B2 --> B1
    B3 --> B1

    C1 --> D1
    C2 --> C1
    C3 --> C1

    D1 --> D2
    D2 --> D3
```

## 2. 瞬时对象/句柄管理

### 2.1 数据结构设计

基于 OP-TEE 的 tee_obj 设计，但适配 Trusty 用户空间环境。tee_obj 既是瞬时对象又是句柄，由 TA 在用户空间自主管理，无需内核维护链表。

#### 2.1.1 核心数据结构（基于 OP-TEE tee_obj，适配 Trusty）

```c
/* Trusty TEE 对象句柄 - 基于 OP-TEE tee_obj 设计，适配用户空间 */
struct trusty_tee_obj {
    /* 链表管理 - 在 libutee 库中维护 */
    struct list_node link;         /* TA 对象链表节点 */

    /* GP 标准对象信息 - 完全兼容 OP-TEE */
    TEE_ObjectInfo info;           /* GP 标准对象信息 */

    /* 并发控制 - 完全保持 OP-TEE 设计 */
    bool busy;                     /* 操作忙标志，防止并发操作 */

    /* 属性管理 - 完全保持 OP-TEE 设计 */
    uint32_t have_attrs;           /* 属性位字段，标识已设置的属性 */
    void *attr;                    /* 属性数据指针 */

    /* 数据流管理 - 完全保持 OP-TEE 设计 */
    size_t ds_pos;                 /* 数据流起始位置偏移 */

    /* 存储连接 - 适配 Trusty 机制 */
    struct trusty_pobj *pobj;      /* 指向持久化对象的指针 */
    handle_t storage_handle;       /* 存储 TA 连接句柄 */

    /* Trusty 用户空间扩展 */
    mutex_t obj_lock;              /* 对象锁 */
};

/* libutee 库中的对象管理上下文 */
struct utee_object_context {
    struct list_node objects;      /* TA 对象链表头 */
    uint32_t object_count;         /* 当前对象数量 */
    uint32_t max_objects;          /* 最大对象数量限制 */
    mutex_t objects_lock;          /* 对象链表锁 */
};

/* 全局对象管理上下文（在 libutee 库中） */
extern struct utee_object_context g_utee_obj_ctx;
```

#### 2.1.2 OP-TEE vs Trusty 对比

| OP-TEE 字段 | Trusty TEE 字段 | 差异说明 |
|-------------|-----------------|----------|
| `TAILQ_ENTRY(tee_obj) link` | `struct list_node link` | 在 libutee 库中维护链表 |
| `TEE_ObjectInfo info` | `TEE_ObjectInfo info` | 完全保持 GP 标准对象信息 |
| `bool busy` | `bool busy` | 完全保持并发控制标志 |
| `uint32_t have_attrs` | `uint32_t have_attrs` | 完全保持属性位字段 |
| `void *attr` | `void *attr` | 完全保持属性数据指针 |
| `size_t ds_pos` | `size_t ds_pos` | 完全保持数据流位置 |
| `struct tee_pobj *pobj` | `struct trusty_pobj *pobj` | 适配 Trusty 持久对象 |
| `struct tee_file_handle *fh` | `handle_t storage_handle` | 适配 Trusty IPC 句柄 |

#### 2.1.3 Trusty 对象管理模型

```mermaid
graph TB
    subgraph "TA 用户空间"
        A[libutee 库]
        A --> B[对象链表管理]
        B --> C[tee_obj 1]
        B --> D[tee_obj 2]
        B --> E[tee_obj N]
        C --> F[存储 TA 连接]
        D --> G[存储 TA 连接]
        E --> H[存储 TA 连接]
    end

    subgraph "存储 TA"
        I[tee_pobj 全局管理]
        F --> I
        G --> I
        H --> I
    end

    subgraph "TA Panic 处理"
        J[TA Panic]
        J --> K[libutee 对象链表自动清理]
        J --> L[通知存储 TA 清理 tee_pobj]
    end
```

### 2.2 管理机制

TA 通过 libutee 库管理 tee_obj，使用链表数据结构。使用对象地址作为 handle，TA panic 时对象链表自动清理。

#### 2.2.1 对象分配操作（libutee 库实现）

```c
/**
 * 分配新的 tee_obj 对象 - libutee 库实现
 * @return: 成功返回对象指针（作为 handle），失败返回 NULL
 */
struct trusty_tee_obj *utee_obj_alloc(void) {
    struct trusty_tee_obj *obj;

    mutex_acquire(&g_utee_obj_ctx.objects_lock);

    /* 检查对象数量限制 */
    if (g_utee_obj_ctx.object_count >= g_utee_obj_ctx.max_objects) {
        mutex_release(&g_utee_obj_ctx.objects_lock);
        return NULL;
    }

    /* 分配对象结构 */
    obj = calloc(1, sizeof(*obj));
    if (!obj) {
        mutex_release(&g_utee_obj_ctx.objects_lock);
        return NULL;
    }

    /* 初始化对象 - 基于 OP-TEE 初始化逻辑 */
    list_initialize(&obj->link);
    memset(&obj->info, 0, sizeof(obj->info));
    obj->busy = false;
    obj->have_attrs = 0;
    obj->attr = NULL;
    obj->ds_pos = 0;
    obj->pobj = NULL;
    obj->storage_handle = INVALID_IPC_HANDLE;
    mutex_init(&obj->obj_lock);

    /* 添加到 libutee 的对象链表 */
    list_add_tail(&g_utee_obj_ctx.objects, &obj->link);
    g_utee_obj_ctx.object_count++;

    mutex_release(&g_utee_obj_ctx.objects_lock);

    /* 返回对象地址作为 handle */
    return obj;
}

/**
 * 释放 tee_obj 对象 - libutee 库实现
 * @param obj: 要释放的对象
 */
void utee_obj_free(struct trusty_tee_obj *obj) {
    if (!obj) {
        return;
    }

    mutex_acquire(&g_utee_obj_ctx.objects_lock);
    mutex_acquire(&obj->obj_lock);

    /* 清理资源 - 基于 OP-TEE 清理逻辑 */
    if (obj->pobj) {
        /* 通知存储 TA 减少持久对象的引用计数 */
        utee_storage_pobj_put(obj->pobj);
        obj->pobj = NULL;
    }

    if (obj->storage_handle != INVALID_IPC_HANDLE) {
        close(obj->storage_handle);
        obj->storage_handle = INVALID_IPC_HANDLE;
    }

    if (obj->attr) {
        /* 安全清除属性数据 */
        memset(obj->attr, 0, obj->have_attrs * sizeof(TEE_Attribute));
        free(obj->attr);
        obj->attr = NULL;
    }

    /* 从链表中移除 */
    list_delete(&obj->link);
    g_utee_obj_ctx.object_count--;

    mutex_release(&obj->obj_lock);
    mutex_destroy(&obj->obj_lock);

    /* 清除对象结构 */
    memset(obj, 0, sizeof(*obj));
    free(obj);

    mutex_release(&g_utee_obj_ctx.objects_lock);
}
```

#### 2.2.2 对象验证操作（libutee 库实现）

```c
/**
 * 验证 handle 是否有效 - libutee 库实现
 * @param handle: 对象 handle（实际是 tee_obj 地址）
 * @return: 成功返回对象指针，失败返回 NULL
 */
struct trusty_tee_obj *utee_obj_get(TEE_ObjectHandle handle) {
    struct trusty_tee_obj *obj = (struct trusty_tee_obj *)handle;
    struct trusty_tee_obj *found_obj = NULL;

    if (!obj) {
        return NULL;
    }

    mutex_acquire(&g_utee_obj_ctx.objects_lock);

    /* 在 libutee 的对象链表中查找，验证 handle 有效性 */
    list_for_every_entry(&g_utee_obj_ctx.objects, found_obj,
                         struct trusty_tee_obj, link) {
        if (found_obj == obj) {
            /* handle 有效，返回对象 */
            mutex_release(&g_utee_obj_ctx.objects_lock);
            return obj;
        }
    }

    mutex_release(&g_utee_obj_ctx.objects_lock);
    return NULL;  /* handle 无效 */
}

/**
 * 设置对象忙状态 - 基于 OP-TEE 逻辑
 * @param obj: 对象指针
 * @return: 成功返回 TEE_SUCCESS，失败返回错误码
 */
TEE_Result utee_obj_set_busy(struct trusty_tee_obj *obj) {
    if (!obj) {
        return TEE_ERROR_BAD_PARAMETERS;
    }

    mutex_acquire(&obj->obj_lock);

    if (obj->busy) {
        mutex_release(&obj->obj_lock);
        return TEE_ERROR_BUSY;
    }

    obj->busy = true;
    mutex_release(&obj->obj_lock);
    return TEE_SUCCESS;
}

/**
 * 清除对象忙状态 - 基于 OP-TEE 逻辑
 * @param obj: 对象指针
 */
void utee_obj_clear_busy(struct trusty_tee_obj *obj) {
    if (!obj) {
        return;
    }

    mutex_acquire(&obj->obj_lock);
    obj->busy = false;
    mutex_release(&obj->obj_lock);
}
```

### 2.3 生命周期管理

在 Trusty 用户空间中，tee_obj 由 libutee 库管理，TA panic 时 libutee 对象链表会自动清理。

#### 2.3.1 TA panic 时的自动清理

```c
/*
 * 重要说明：在 Trusty 用户空间环境中，TA panic 时的处理与 OP-TEE 不同
 *
 * OP-TEE：
 * - tee_obj 在内核中，需要内核主动清理
 * - 内核维护 TA 上下文中的对象链表
 * - panic 时遍历链表清理所有对象
 *
 * Trusty：
 * - tee_obj 在 TA 用户空间的 libutee 库中
 * - TA panic 时，整个用户空间被销毁
 * - libutee 库中的对象链表自动清理
 *
 * 真正需要处理的是：清理该 TA 在存储 TA 中的持久化对象
 */

/**
 * TA panic 时的处理流程 - Trusty 版本
 * @param ta_uuid: panic 的 TA UUID
 */
void handle_ta_panic(const struct uuid *ta_uuid) {
    /*
     * 步骤 1: tee_obj 自动清理
     * - TA 用户空间被销毁时，libutee 库中的所有 tee_obj 自动释放
     * - 包括对象链表、内存、锁等资源
     * - 无需特殊处理
     */

    /*
     * 步骤 2: 通知存储 TA 清理该 TA 的持久化对象
     * - 这是唯一需要主动处理的部分
     */
    notify_storage_ta_cleanup(ta_uuid);
}

/**
 * libutee 库初始化 - 在 TA 启动时调用
 */
void utee_obj_context_init(void) {
    list_initialize(&g_utee_obj_ctx.objects);
    g_utee_obj_ctx.object_count = 0;
    g_utee_obj_ctx.max_objects = MAX_OBJECTS_PER_TA;
    mutex_init(&g_utee_obj_ctx.objects_lock);
}

/**
 * libutee 库清理 - 在 TA 退出时调用（可选）
 */
void utee_obj_context_cleanup(void) {
    struct trusty_tee_obj *obj, *temp;

    mutex_acquire(&g_utee_obj_ctx.objects_lock);

    /* 清理所有剩余对象 */
    list_for_every_entry_safe(&g_utee_obj_ctx.objects, obj, temp,
                              struct trusty_tee_obj, link) {
        utee_obj_free(obj);
    }

    mutex_release(&g_utee_obj_ctx.objects_lock);
    mutex_destroy(&g_utee_obj_ctx.objects_lock);
}
```

## 3. 持久化对象管理

### 3.1 存储 TA 架构分析

基于对 Trusty 存储 TA 代码的深入分析，存储 TA 采用了与传统设计不同的架构。实际的 Trusty 存储 TA 主要负责底层文件系统管理，而不是直接管理 GP 对象。

#### 3.1.1 Trusty 存储 TA 实际架构

**存储 TA 核心组件：**

```c
/* 实际的存储会话结构 - 基于代码分析 */
struct storage_session {
    uint32_t magic;                      /* 会话魔数 STORAGE_SESSION_MAGIC */
    struct block_device_tipc block_device; /* 块设备接口 */
    struct key key;                      /* 存储加密密钥 */
    struct ipc_channel_context proxy_ctx; /* IPC 通信上下文 */
};

/* 客户端会话结构 - 每个连接的 TA 一个 */
struct storage_client_session {
    uint32_t magic;                      /* 会话魔数 STORAGE_CLIENT_SESSION_MAGIC */
    struct transaction tr;               /* 事务上下文 */
    uuid_t uuid;                        /* 客户端 TA UUID */
    struct file_handle** files;         /* 文件句柄数组 */
    size_t files_count;                 /* 文件句柄数量 */
    struct ipc_channel_context context; /* IPC 通信上下文 */
};

/* 文件句柄结构 - 实际的文件操作单元 */
struct file_handle {
    struct list_node node;              /* 事务中的文件链表节点 */
    struct block_mac to_commit_block_mac; /* 待提交的块 MAC */
    struct block_mac committed_block_mac; /* 已提交的块 MAC */
    struct block_mac block_mac;          /* 当前块 MAC */
    data_block_t to_commit_size;        /* 待提交的文件大小 */
    data_block_t size;                  /* 当前文件大小 */
    bool used_by_tr;                    /* 是否被事务使用 */
};

/* 事务结构 - 文件系统事务管理 */
struct transaction {
    struct list_node node;              /* 文件系统事务链表节点 */
    struct fs* fs;                      /* 文件系统状态 */
    struct list_node open_files;        /* 打开的文件链表 */
    bool failed;                        /* 事务失败标志 */
    bool invalid_block_found;           /* 发现无效块标志 */
    bool complete;                      /* 事务完成标志 */
    bool rebuild_free_set;              /* 重建空闲集合标志 */
    bool repaired;                      /* 修复标志 */

    /* 块管理 */
    struct block_set tmp_allocated;     /* 临时分配的块 */
    struct block_set allocated;         /* 已分配的块 */
    struct block_set freed;             /* 已释放的块 */

    /* 文件操作跟踪 */
    struct block_tree files_added;      /* 添加的文件 */
    struct block_tree files_updated;    /* 更新的文件 */
    struct block_tree files_removed;    /* 删除的文件 */
};
```

#### 3.1.2 Trusty 存储 TA 实际架构

```mermaid
graph TB
    subgraph "存储 TA 进程"
        A[main.c - 服务入口]
        A --> B[proxy.c - 代理服务]
        A --> C[client_tipc.c - 客户端处理]

        B --> D[storage_session<br/>代理会话管理]
        C --> E[storage_client_session<br/>客户端会话管理]

        E --> F[transaction<br/>事务管理]
        E --> G[file_handle 数组<br/>文件句柄管理]

        F --> H[file.c - 文件操作]
        F --> I[block_*.c - 块设备管理]

        H --> J[实际文件系统操作]
        I --> K[RPMB/存储后端]
    end

    subgraph "客户端 TA"
        L[GP 存储 API 调用]
        L --> M[TIPC 消息]
        M --> C
    end

    subgraph "会话隔离机制"
        N[每个 TA 独立的 client_session]
        N --> O[基于 TA UUID 的路径隔离]
        N --> P[独立的文件句柄空间]
        N --> Q[独立的事务上下文]
    end
```

#### 3.1.3 存储 TA 关键特性分析

**基于代码分析的关键发现：**

1. **会话隔离机制**：
   - 每个连接的 TA 都有独立的 `storage_client_session`
   - 通过 `uuid_t uuid` 字段标识客户端 TA
   - 每个会话维护独立的文件句柄数组和事务上下文

2. **文件路径隔离**：
   - 使用 `get_path()` 函数基于 TA UUID 生成隔离路径
   - 路径格式：`{ta_uuid}/{filename}`
   - 确保不同 TA 无法访问彼此的文件

3. **事务管理**：
   - 每个客户端会话有独立的事务上下文
   - 支持原子操作和回滚机制
   - 文件操作在事务中进行，确保一致性

4. **文件句柄管理**：
   - 动态分配文件句柄数组
   - 支持最大 `STORAGE_MAX_OPEN_FILES` 个并发文件
   - 自动回收和压缩句柄空间

5. **无需全局对象管理**：
   - 存储 TA 不维护全局的持久对象列表
   - 对象管理通过文件系统路径和文件句柄实现
   - 简化了对象生命周期管理

### 3.2 存储 TA 会话管理机制

基于代码分析，存储 TA 采用会话隔离而非全局对象管理的方式。

#### 3.2.1 会话生命周期管理

**会话创建流程：**

```c
/* 客户端连接时创建会话 - 基于实际代码 */
struct ipc_channel_context* client_connect(struct ipc_port_context* parent_ctx,
                                          const uuid_t* peer_uuid,
                                          handle_t chan_handle) {
    struct storage_client_session* client_session;

    /* 分配客户端会话 */
    client_session = calloc(1, sizeof(*client_session));
    client_session->magic = STORAGE_CLIENT_SESSION_MAGIC;

    /* 初始化文件句柄数组 */
    client_session->files = NULL;
    client_session->files_count = 0;

    /* 初始化事务上下文 */
    transaction_init(&client_session->tr, tr_state, false);

    /* 缓存客户端 TA UUID */
    memcpy(&client_session->uuid, peer_uuid, sizeof(*peer_uuid));

    return &client_session->context;
}
```

**会话清理流程：**

```c
/* 客户端断开时清理会话 - 基于实际代码 */
static void client_disconnect(struct ipc_channel_context* context) {
    struct storage_client_session* session =
        chan_context_to_client_session(context);

    /* 关闭所有打开的文件 */
    session_close_all_files(session);

    /* 清理事务资源 */
    transaction_free(&session->tr);

    /* 释放会话内存 */
    free(session);
}
```

#### 3.2.2 文件句柄管理实现

**动态文件句柄分配：**

```c
/* 创建文件句柄 - 基于实际代码 */
static enum storage_err create_file_handle(
        struct storage_client_session* session,
        uint32_t* handlep,
        struct file_handle** file_p) {
    enum storage_err result;
    uint32_t handle;
    struct file_handle* file;

    /* 查找空闲句柄槽位 */
    for (handle = 0; handle < session->files_count; handle++)
        if (!session->files[handle])
            break;

    /* 需要扩展句柄数组 */
    if (handle >= session->files_count) {
        result = session_set_files_count(session, handle + 1);
        if (result != STORAGE_NO_ERROR)
            return result;
    }

    /* 分配文件句柄 */
    file = calloc(1, sizeof(*file));
    if (!file) {
        return STORAGE_ERR_GENERIC;
    }

    session->files[handle] = file;
    *handlep = handle;
    *file_p = file;
    return STORAGE_NO_ERROR;
}

/* 释放文件句柄 - 基于实际代码 */
static void free_file_handle(struct storage_client_session* session,
                             uint32_t handle) {
    if (handle >= session->files_count || !session->files[handle]) {
        return;
    }

    free(session->files[handle]);
    session->files[handle] = NULL;

    /* 压缩句柄数组 */
    session_shrink_files(session);
}
```

#### 3.2.3 路径隔离机制

**基于 TA UUID 的路径生成：**

```c
/* 路径生成函数 - 基于实际代码 */
static int get_path(char* path_out,
                    size_t path_out_size,
                    const uuid_t* uuid,
                    const char* file_name,
                    size_t file_name_len) {
    unsigned int rc;

    /* 生成基于 TA UUID 的路径前缀 */
    rc = snprintf(path_out, path_out_size,
                  "%08x%04x%04x%02x%02x%02x%02x%02x%02x%02x%02x/",
                  uuid->time_low, uuid->time_mid, uuid->time_hi_and_version,
                  uuid->clock_seq_and_node[0], uuid->clock_seq_and_node[1],
                  uuid->clock_seq_and_node[2], uuid->clock_seq_and_node[3],
                  uuid->clock_seq_and_node[4], uuid->clock_seq_and_node[5],
                  uuid->clock_seq_and_node[6], uuid->clock_seq_and_node[7]);

    if (rc + file_name_len >= path_out_size) {
        return STORAGE_ERR_NOT_VALID;
    }

    /* 追加文件名 */
    memcpy(path_out + rc, file_name, file_name_len);
    path_out[rc + file_name_len] = '\0';

    return STORAGE_NO_ERROR;
}
```

**文件名验证：**

```c
/* 文件名合法性检查 - 基于实际代码 */
static int is_valid_name(const char* name, size_t name_len) {
    size_t i;

    if (!name_len)
        return 0;

    /* 只允许 [a-z][A-Z][0-9][.-_] 字符 */
    for (i = 0; i < name_len; i++) {
        if ((name[i] >= 'a') && (name[i] <= 'z'))
            continue;
        if ((name[i] >= 'A') && (name[i] <= 'Z'))
            continue;
        if ((name[i] >= '0') && (name[i] <= '9'))
            continue;
        if ((name[i] == '.') || (name[i] == '-') || (name[i] == '_'))
            continue;

        return 0;  /* 非法字符 */
    }

    return 1;
}
```

### 3.3 存储操作实现

基于代码分析，存储 TA 通过文件系统接口提供存储服务，而不是直接管理对象。

#### 3.3.1 文件操作接口

**文件打开操作：**

```c
/* 文件打开 - 基于实际代码 */
static int storage_file_open(struct storage_msg* msg,
                             struct storage_file_open_req* req,
                             size_t req_size,
                             struct storage_client_session* session) {
    enum file_op_result open_result;
    enum storage_err result;
    struct file_handle* file = NULL;
    const char* fname;
    size_t fname_len;
    uint32_t flags, f_handle;
    char path_buf[FS_PATH_MAX];
    enum file_create_mode file_create_mode;

    /* 验证请求参数 */
    if (req_size < sizeof(*req)) {
        return STORAGE_ERR_NOT_VALID;
    }

    flags = req->flags;
    fname = req->name;
    fname_len = req_size - sizeof(*req);

    /* 验证文件名合法性 */
    if (!is_valid_name(fname, fname_len)) {
        return STORAGE_ERR_NOT_VALID;
    }

    /* 生成隔离路径 */
    result = get_path(path_buf, sizeof(path_buf), &session->uuid, fname, fname_len);
    if (result != STORAGE_NO_ERROR) {
        return result;
    }

    /* 创建文件句柄 */
    result = create_file_handle(session, &f_handle, &file);
    if (result != STORAGE_NO_ERROR) {
        return result;
    }

    /* 确定文件创建模式 */
    if (flags & STORAGE_FILE_OPEN_CREATE) {
        if (flags & STORAGE_FILE_OPEN_CREATE_EXCLUSIVE) {
            file_create_mode = FILE_OPEN_CREATE_EXCLUSIVE;
        } else {
            file_create_mode = FILE_OPEN_CREATE;
        }
    } else {
        file_create_mode = FILE_OPEN_NO_CREATE;
    }

    /* 执行文件打开操作 */
    open_result = file_open(&session->tr, path_buf, file, file_create_mode,
                           msg->flags & STORAGE_MSG_FLAG_FS_REPAIRED_ACK);

    if (open_result != FILE_OP_SUCCESS) {
        free_file_handle(session, f_handle);
        return file_op_result_to_storage_err(open_result);
    }

    /* 返回文件句柄 */
    return send_response(session, STORAGE_NO_ERROR, msg, &f_handle, sizeof(f_handle));
}
```

#### 3.3.2 事务管理机制

**事务初始化和管理：**

```c
/* 事务初始化 - 基于实际代码 */
void transaction_init(struct transaction* tr, struct fs* fs, bool activate) {
    memset(tr, 0, sizeof(*tr));
    list_initialize(&tr->node);
    list_initialize(&tr->open_files);
    tr->fs = fs;

    if (activate) {
        transaction_activate(tr);
    }
}

/* 事务激活 - 基于实际代码 */
void transaction_activate(struct transaction* tr) {
    assert(tr->fs);
    assert(!transaction_is_active(tr));

    tr->failed = false;
    tr->invalid_block_found = false;
    tr->complete = false;
    tr->rebuild_free_set = false;
    tr->repaired = false;

    /* 初始化块集合 */
    block_set_init(tr->fs, &tr->tmp_allocated);
    block_set_init(tr->fs, &tr->allocated);
    block_set_init(tr->fs, &tr->freed);

    /* 初始化文件树 */
    fs_file_tree_init(tr->fs, &tr->files_added);
    fs_file_tree_init(tr->fs, &tr->files_updated);
    fs_file_tree_init(tr->fs, &tr->files_removed);

    /* 添加到活动事务列表 */
    list_add_tail(&tr->fs->allocated, &tr->allocated.node);
    list_add_tail(&tr->fs->allocated, &tr->tmp_allocated.node);
}

/* 事务提交 - 基于实际代码 */
void transaction_complete_etc(struct transaction* tr, bool update_checkpoint) {
    if (tr->failed) {
        return;
    }

    /* 执行事务提交逻辑 */
    /* ... 复杂的文件系统事务提交过程 ... */

    tr->complete = true;
}
```

#### 3.3.3 TA Panic 处理机制

**基于会话的自动清理：**

```c
/* TA 断开连接时的自动清理 - 基于实际代码 */
static void client_disconnect(struct ipc_channel_context* context) {
    struct storage_client_session* session =
        chan_context_to_client_session(context);

    /* 自动关闭所有打开的文件 */
    session_close_all_files(session);

    /* 清理事务资源 */
    transaction_free(&session->tr);

    /* 释放会话内存 */
    free(session);
}

/* 关闭所有文件 - 基于实际代码 */
static void session_close_all_files(struct storage_client_session* session) {
    uint32_t f_handle;
    struct file_handle* file;

    for (f_handle = 0; f_handle < session->files_count; f_handle++) {
        file = session->files[f_handle];
        if (file) {
            file_close(file);  /* 关闭文件，释放文件系统资源 */
            free(file);        /* 释放文件句柄内存 */
        }
    }

    if (session->files) {
        free(session->files);  /* 释放文件句柄数组 */
    }
    session->files_count = 0;
}
```

**关键设计特点：**

1. **无需全局对象清理**：TA panic 时，对应的客户端会话自动断开
2. **自动资源回收**：会话断开时自动关闭所有文件句柄和事务
3. **文件系统一致性**：通过事务机制确保文件系统状态一致
4. **简化的清理逻辑**：不需要遍历全局对象列表进行清理

## 4. 设计总结

### 4.1 Trusty 存储 TA 架构特点

基于对实际代码的深入分析，Trusty 存储 TA 的设计具有以下特点：

#### 4.1.1 会话隔离架构

**核心设计原则：**
- **会话隔离**：每个连接的 TA 都有独立的客户端会话
- **路径隔离**：基于 TA UUID 生成隔离的文件路径
- **资源隔离**：每个会话维护独立的文件句柄和事务上下文
- **自动清理**：TA 断开时自动清理所有相关资源

#### 4.1.2 与传统设计的差异

**传统 OP-TEE 设计：**
- 全局持久对象管理
- 复杂的对象引用计数
- 需要主动的 panic 清理机制

**Trusty 存储 TA 设计：**
- 基于会话的资源管理
- 文件系统级别的隔离
- 自动的资源清理机制
- 简化的对象生命周期

### 4.2 对 GP 存储设计的影响

基于 Trusty 存储 TA 的实际架构，需要重新考虑 GP 存储的设计策略。

#### 4.2.1 设计建议

**推荐的 GP 存储架构：**

1. **用户空间对象管理**：
   - 在 libutee 库中实现完整的 GP 对象抽象
   - 瞬态对象完全在用户空间管理
   - 持久对象通过存储 TA 的文件接口实现

2. **简化的存储后端**：
   - 直接使用 Trusty 存储 TA 的文件接口
   - 利用现有的路径隔离和会话管理机制
   - 无需实现复杂的对象引用计数

3. **自动资源管理**：
   - 利用存储 TA 的自动清理机制
   - TA panic 时无需特殊的对象清理逻辑
   - 简化错误处理和资源回收

#### 4.2.2 实现优势

**相比传统 OP-TEE 设计的优势：**

1. **简化的架构**：无需复杂的全局对象管理
2. **更好的隔离**：基于文件系统的天然隔离
3. **自动清理**：利用会话断开的自动清理机制
4. **更好的可维护性**：减少了状态管理的复杂性

**需要注意的限制：**

1. **性能考虑**：每个对象操作都需要 TIPC 通信
2. **并发限制**：受存储 TA 的文件句柄数量限制
3. **兼容性**：需要确保与 GP 标准的完全兼容

#### 4.2.3 GP API 到存储 TA 的映射

**持久对象操作映射：**

| GP API | 存储 TA 操作 | 实现说明 |
|--------|-------------|----------|
| `TEE_CreatePersistentObject` | `storage_file_open` + CREATE | 创建文件并返回句柄 |
| `TEE_OpenPersistentObject` | `storage_file_open` | 打开现有文件 |
| `TEE_CloseObject` | `storage_file_close` | 关闭文件句柄 |
| `TEE_ReadObjectData` | `storage_file_read` | 读取文件数据 |
| `TEE_WriteObjectData` | `storage_file_write` | 写入文件数据 |
| `TEE_TruncateObjectData` | `storage_file_set_size` | 设置文件大小 |
| `TEE_RenamePersistentObject` | `storage_file_move` | 重命名文件 |

**瞬态对象操作：**
- 完全在用户空间 libutee 中实现
- 不涉及存储 TA 交互
- 使用内存管理和链表维护

## 5. 总结

### 5.1 关键发现

通过对 Trusty 存储 TA 代码的深入分析，发现了与传统 OP-TEE 设计的重要差异：

1. **会话隔离架构**：Trusty 存储 TA 采用基于会话的隔离机制，而非全局对象管理
2. **自动资源清理**：TA 断开时自动清理所有相关资源，无需复杂的 panic 处理
3. **文件系统抽象**：通过文件系统接口提供存储服务，简化了对象管理
4. **路径隔离**：基于 TA UUID 的路径隔离确保安全性

### 5.2 设计建议

基于分析结果，对 GP 存储设计的建议：

1. **采用用户空间对象管理**：在 libutee 库中实现 GP 对象抽象
2. **利用现有存储架构**：直接使用 Trusty 存储 TA 的文件接口
3. **简化资源管理**：利用会话断开的自动清理机制
4. **确保 GP 兼容性**：在简化架构的基础上保持与 GP 标准的完全兼容

### 5.3 后续工作

1. **完善 GP 存储 API 实现**：基于分析结果完善用户空间的 GP 存储 API
2. **性能优化**：针对 TIPC 通信开销进行优化
3. **测试验证**：确保与 GP 标准的完全兼容性
4. **文档更新**：更新相关设计文档以反映实际架构

















































