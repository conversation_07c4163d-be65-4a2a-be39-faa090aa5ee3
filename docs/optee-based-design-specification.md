# 基于 OP-TEE 双层对象模型的 Trusty TEE GP 存储设计方案

## 1. 引言

### 1.1 设计目标
基于 OP-TEE 成熟的双层对象模型（tee_obj + tee_pobj），为 Trusty TEE 设计完整的 GP 标准存储实现方案。该方案充分借鉴 OP-TEE 的设计精髓，同时完美适配 Trusty TEE 的架构特点。

### 1.2 设计原则
1. **OP-TEE 架构完全复制**：数据结构、并发控制、生命周期管理完全基于 OP-TEE 实现
2. **Trusty 存储深度集成**：通过适配层完美集成 Trusty 现有存储服务
3. **GP 标准严格遵循**：26 个 GP 存储 API 完全符合 GP TEE Internal Core API v1.3.1 规范
4. **轻量化设计优化**：纯用户空间实现，避免内核修改，控制复杂度

### 1.3 架构概览
```mermaid
graph TB
    subgraph "GP Storage API Layer"
        A1[Generic Object APIs<br/>5个函数]
        A2[Transient Object APIs<br/>7个函数]
        A3[Persistent Object APIs<br/>4个函数]
        A4[Enumeration APIs<br/>5个函数]
        A5[Data Stream APIs<br/>4个函数]
        A6[Key Generation API<br/>1个函数]
    end

    subgraph "OP-TEE Dual-Layer Object Model"
        B1[trusty_tee_obj<br/>对象句柄层]
        B2[trusty_pobj<br/>持久对象层]
        B3[trusty_storage_ops<br/>存储操作接口]
    end

    subgraph "Trusty Storage Backend"
        C1[Storage Library<br/>用户空间存储库]
        C2[Storage Service<br/>存储服务]
        C3[File System<br/>文件系统]
    end

    A1 --> B1
    A2 --> B1
    A3 --> B1
    A4 --> B1
    A5 --> B1
    A6 --> B1

    B1 --> B2
    B1 --> B3
    B2 --> B3

    B3 --> C1
    C1 --> C2
    C2 --> C3
```

## 2. 核心数据结构设计

### 2.1 对象句柄结构（基于 OP-TEE tee_obj 完全适配）

#### 2.1.1 数据结构定义
```c
/* Trusty TEE 对象句柄 - 完全基于 OP-TEE tee_obj 设计 */
struct trusty_tee_obj {
    /* 链表管理 - 对应 OP-TEE 的 TAILQ_ENTRY */
    struct list_node link;         /* TA 私有对象链表节点 */

    /* GP 标准对象信息 - 完全兼容 OP-TEE */
    TEE_ObjectInfo info;           /* GP 标准对象信息 */

    /* 并发控制 - 完全保持 OP-TEE 设计 */
    bool busy;                     /* 操作忙标志，防止并发操作 */

    /* 属性管理 - 完全保持 OP-TEE 设计 */
    uint32_t have_attrs;           /* 属性位字段，标识已设置的属性 */
    void *attr;                    /* 属性数据指针 */

    /* 数据流管理 - 完全保持 OP-TEE 设计 */
    size_t ds_pos;                 /* 数据流起始位置偏移 */

    /* 存储抽象 - 适配 Trusty 机制 */
    struct trusty_pobj *pobj;      /* 指向持久化对象的指针 */
    file_handle_t fh;              /* Trusty 文件句柄 */

    /* Trusty 特有扩展 */
    uint32_t handle_id;            /* 句柄唯一 ID */
    mutex_t obj_lock;              /* 对象锁 */
};
```

#### 2.1.2 OP-TEE 字段映射表

| OP-TEE 字段 | Trusty TEE 字段 | 映射说明 |
|-------------|-----------------|----------|
| `TAILQ_ENTRY(tee_obj) link` | `struct list_node link` | 链表节点，适配 Trusty 链表 |
| `TEE_ObjectInfo info` | `TEE_ObjectInfo info` | 完全保持 GP 标准对象信息 |
| `bool busy` | `bool busy` | 完全保持并发控制标志 |
| `uint32_t have_attrs` | `uint32_t have_attrs` | 完全保持属性位字段 |
| `void *attr` | `void *attr` | 完全保持属性数据指针 |
| `size_t ds_pos` | `size_t ds_pos` | 完全保持数据流位置 |
| `struct tee_pobj *pobj` | `struct trusty_pobj *pobj` | 适配 Trusty 持久对象 |
| `struct tee_file_handle *fh` | `file_handle_t fh` | 适配 Trusty 文件句柄 |

### 2.2 持久对象结构（基于 OP-TEE tee_pobj 完全适配）

#### 2.2.1 数据结构定义
```c
/* Trusty 持久对象 - 完全基于 OP-TEE tee_pobj 设计 */
struct trusty_pobj {
    /* 链表管理 - 对应 OP-TEE 的 TAILQ_ENTRY */
    struct list_node link;        /* 全局持久化对象链表节点 */

    /* 引用计数 - 完全保持 OP-TEE 设计 */
    uint32_t refcnt;              /* 引用计数，支持多句柄访问同一对象 */

    /* TA 隔离 - 完全保持 OP-TEE 设计 */
    struct uuid uuid;             /* 拥有该对象的 TA 的 UUID，实现 TA 隔离 */

    /* 对象标识 - 完全保持 OP-TEE 设计 */
    void *obj_id;                 /* 对象标识符，由 TA 指定 */
    uint32_t obj_id_len;          /* 对象标识符长度 */

    /* 访问控制 - 完全保持 OP-TEE 设计 */
    uint32_t flags;               /* 访问标志（读/写/共享权限） */
    uint32_t obj_info_usage;      /* 对象使用权限（密钥用途等） */

    /* 状态管理 - 完全保持 OP-TEE 设计 */
    bool temporary;               /* 临时对象标志，创建过程中为 true */
    bool creating;                /* 创建中标志，防止并发访问冲突 */

    /* 存储操作接口 - 对应 OP-TEE 的 tee_file_operations */
    const struct trusty_storage_ops *fops;

    /* Trusty 特有扩展 */
    storage_session_t session;    /* Trusty 存储会话 */
    char storage_path[256];       /* 存储路径 */
    mutex_t pobj_lock;            /* 持久对象锁 */
};
```

#### 2.2.2 OP-TEE 字段映射表

| OP-TEE 字段 | Trusty TEE 字段 | 映射说明 |
|-------------|-----------------|----------|
| `TAILQ_ENTRY(tee_pobj) link` | `struct list_node link` | 全局持久对象链表节点 |
| `uint32_t refcnt` | `uint32_t refcnt` | 完全保持引用计数机制 |
| `struct tee_ta_session *ta_session` | `struct uuid uuid` | 适配为 TA UUID 标识 |
| `void *obj_id` | `void *obj_id` | 完全保持对象标识符 |
| `uint32_t obj_id_len` | `uint32_t obj_id_len` | 完全保持标识符长度 |
| `uint32_t flags` | `uint32_t flags` | 完全保持访问标志 |
| `uint32_t obj_info_usage` | `uint32_t obj_info_usage` | 完全保持使用权限 |
| `bool temporary` | `bool temporary` | 完全保持临时对象标志 |
| `bool creating` | `bool creating` | 完全保持创建中标志 |
| `const struct tee_file_operations *fops` | `const struct trusty_storage_ops *fops` | 适配 Trusty 存储操作接口 |

### 2.3 存储操作接口（基于 OP-TEE tee_file_operations 适配）

#### 2.3.1 接口结构定义
```c
/* Trusty 存储操作接口 - 完全基于 OP-TEE tee_file_operations 设计 */
struct trusty_storage_ops {
    /* 基础文件操作 - 对应 OP-TEE 接口 */
    TEE_Result (*open)(struct trusty_pobj *pobj, size_t *size,
                      file_handle_t *fh);
    TEE_Result (*create)(struct trusty_pobj *pobj, bool overwrite,
                        const void *head, size_t head_size,
                        const void *attr, size_t attr_size,
                        file_handle_t *fh);
    TEE_Result (*close)(file_handle_t *fh);
    TEE_Result (*read)(file_handle_t fh, size_t pos,
                      void *buf, size_t *len);
    TEE_Result (*write)(file_handle_t fh, size_t pos,
                       const void *buf, size_t len);
    TEE_Result (*truncate)(file_handle_t fh, size_t len);
    TEE_Result (*remove)(struct trusty_pobj *pobj);
    TEE_Result (*rename)(struct trusty_pobj *old_pobj,
                        struct trusty_pobj *new_pobj,
                        bool overwrite);

    /* 枚举操作 - 对应 OP-TEE 的目录操作 */
    TEE_Result (*opendir)(uint32_t storage_id, void **dir_handle);
    TEE_Result (*readdir)(void *dir_handle, struct trusty_pobj **pobj);
    TEE_Result (*closedir)(void *dir_handle);

    /* 扩展操作 - Trusty 特有功能 */
    TEE_Result (*get_file_size)(file_handle_t fh, size_t *size);
    TEE_Result (*file_exists)(struct trusty_pobj *pobj, bool *exists);
};
```

#### 2.3.2 OP-TEE 接口映射

| OP-TEE 接口 | Trusty TEE 接口 | 实现说明 |
|-------------|-----------------|----------|
| `open()` | `open()` | 使用 `storage_open_file()` 实现 |
| `create()` | `create()` | 使用 `storage_open_file()` + CREATE 标志实现 |
| `close()` | `close()` | 使用 `storage_close_file()` 实现 |
| `read()` | `read()` | 使用 `storage_read()` 实现 |
| `write()` | `write()` | 使用 `storage_write()` 实现 |
| `truncate()` | `truncate()` | 使用 `storage_set_file_size()` 实现 |
| `remove()` | `remove()` | 使用 `storage_delete_file()` 实现 |
| `rename()` | `rename()` | 使用 `storage_move_file()` 实现 |

## 3. OP-TEE 并发控制机制适配

### 3.1 三重并发保护机制

#### 3.1.1 OP-TEE 并发控制策略
OP-TEE 采用三重并发保护机制：
1. **busy 标志保护**：防止对象句柄的并发操作
2. **creating 标志保护**：防止持久对象创建过程中的并发访问
3. **引用计数保护**：管理持久对象的多句柄访问

#### 3.1.2 Trusty TEE 并发控制适配实现

**对象忙状态管理：**
```c
/* 对象忙状态管理 - 完全基于 OP-TEE 逻辑 */
TEE_Result trusty_obj_set_busy(struct trusty_tee_obj *obj) {
    mutex_acquire(&obj->obj_lock);

    if (obj->busy) {
        mutex_release(&obj->obj_lock);
        return TEE_ERROR_BUSY;
    }

    obj->busy = true;
    mutex_release(&obj->obj_lock);
    return TEE_SUCCESS;
}

void trusty_obj_clear_busy(struct trusty_tee_obj *obj) {
    mutex_acquire(&obj->obj_lock);
    obj->busy = false;
    mutex_release(&obj->obj_lock);
}
```

**持久对象引用计数管理：**
```c
/* 持久对象引用计数管理 - 完全基于 OP-TEE 逻辑 */
void trusty_pobj_get(struct trusty_pobj *pobj) {
    mutex_acquire(&global_pobj_list_lock);
    pobj->refcnt++;
    mutex_release(&global_pobj_list_lock);
}

TEE_Result trusty_pobj_put(struct trusty_pobj *pobj) {
    if (!pobj)
        return TEE_SUCCESS;

    mutex_acquire(&global_pobj_list_lock);
    if (--pobj->refcnt == 0) {
        /* 引用计数为 0，从列表中移除并释放 */
        list_delete(&pobj->link);
        mutex_release(&global_pobj_list_lock);

        if (pobj->obj_id)
            free(pobj->obj_id);
        mutex_destroy(&pobj->pobj_lock);
        free(pobj);
    } else {
        mutex_release(&global_pobj_list_lock);
    }

    return TEE_SUCCESS;
}
```

**创建状态保护：**
```c
/* 创建状态保护 - 完全基于 OP-TEE 逻辑 */
TEE_Result trusty_pobj_check_access_conflict(struct trusty_pobj *pobj,
                                           uint32_t flags) {
    if (pobj->creating) {
        return TEE_ERROR_BUSY;  /* 对象创建中，拒绝访问 */
    }

    /* 检查访问标志冲突 */
    if ((flags & TEE_DATA_FLAG_ACCESS_WRITE) &&
        (pobj->flags & TEE_DATA_FLAG_SHARE_WRITE) == 0) {
        return TEE_ERROR_ACCESS_CONFLICT;
    }

    return TEE_SUCCESS;
}
```

### 3.2 生命周期管理

#### 3.2.1 对象句柄生命周期
```c
/* 对象句柄分配 - 基于 OP-TEE 逻辑 */
struct trusty_tee_obj *trusty_obj_alloc(void) {
    struct trusty_tee_obj *obj = calloc(1, sizeof(*obj));
    if (!obj)
        return NULL;

    /* 初始化对象状态 - 基于 OP-TEE 初始化逻辑 */
    list_initialize(&obj->link);
    memset(&obj->info, 0, sizeof(obj->info));
    obj->busy = false;
    obj->have_attrs = 0;
    obj->attr = NULL;
    obj->ds_pos = 0;
    obj->pobj = NULL;
    obj->fh = INVALID_FILE_HANDLE;
    obj->handle_id = trusty_obj_alloc_handle_id();
    mutex_init(&obj->obj_lock);

    return obj;
}

/* 对象句柄释放 - 基于 OP-TEE 逻辑 */
void trusty_obj_free(struct trusty_tee_obj *obj) {
    if (!obj)
        return;

    /* 清理资源 - 基于 OP-TEE 清理逻辑 */
    if (obj->pobj) {
        trusty_pobj_put(obj->pobj);
        obj->pobj = NULL;
    }

    if (obj->fh != INVALID_FILE_HANDLE) {
        storage_close_file(obj->fh);
        obj->fh = INVALID_FILE_HANDLE;
    }

    if (obj->attr) {
        free(obj->attr);
        obj->attr = NULL;
    }

    mutex_destroy(&obj->obj_lock);
    free(obj);
}
```

#### 3.2.2 持久对象生命周期
```c
/* 持久对象查找或创建 - 基于 OP-TEE 逻辑 */
struct trusty_pobj *trusty_pobj_find_or_create(const void *obj_id,
                                              uint32_t obj_id_len,
                                              const char *storage_path) {
    struct trusty_pobj *pobj;

    mutex_acquire(&global_pobj_list_lock);

    /* 查找现有对象 */
    list_for_every_entry(&global_pobj_list, pobj, struct trusty_pobj, link) {
        if (pobj->obj_id_len == obj_id_len &&
            memcmp(pobj->obj_id, obj_id, obj_id_len) == 0) {
            trusty_pobj_get(pobj);
            mutex_release(&global_pobj_list_lock);
            return pobj;
        }
    }

    /* 创建新对象 */
    pobj = calloc(1, sizeof(*pobj));
    if (!pobj) {
        mutex_release(&global_pobj_list_lock);
        return NULL;
    }

    /* 初始化持久对象 - 基于 OP-TEE 初始化逻辑 */
    list_initialize(&pobj->link);
    pobj->refcnt = 1;
    pobj->obj_id = malloc(obj_id_len);
    if (!pobj->obj_id) {
        free(pobj);
        mutex_release(&global_pobj_list_lock);
        return NULL;
    }
    memcpy(pobj->obj_id, obj_id, obj_id_len);
    pobj->obj_id_len = obj_id_len;
    strncpy(pobj->storage_path, storage_path, sizeof(pobj->storage_path) - 1);
    pobj->temporary = false;
    pobj->creating = false;
    mutex_init(&pobj->pobj_lock);

    list_add_tail(&global_pobj_list, &pobj->link);
    mutex_release(&global_pobj_list_lock);

    return pobj;
}
```

## 4. GP 存储文件格式设计

### 4.1 基于 OP-TEE 的存储格式

#### 4.1.1 OP-TEE 存储文件格式参考
OP-TEE 使用 `tee_svc_storage_head` 结构作为 GP 标准存储文件头部：

```c
/* OP-TEE 存储文件头部结构 */
struct tee_svc_storage_head {
    size_t attr_size;        /* 属性数据大小 */
    size_t objectSize;       /* 对象大小（位） */
    size_t maxObjectSize;    /* 最大对象大小（位） */
    uint32_t objectUsage;    /* 对象使用权限 */
    uint32_t objectType;     /* 对象类型 */
    uint32_t have_attrs;     /* 属性位字段 */
};
```

#### 4.1.2 Trusty TEE 适配的存储格式

```c
/* Trusty TEE GP 对象文件头部 - 基于 OP-TEE 格式扩展 */
struct trusty_gp_object_header {
    /* 文件标识和版本 */
    uint32_t magic;              /* 魔数：'TGPO' (Trusty GP Object) */
    uint32_t version;            /* 格式版本号 */
    uint32_t header_size;        /* 头部大小 */
    uint32_t total_size;         /* 文件总大小 */

    /* OP-TEE 兼容字段 - 完全保持 */
    size_t attr_size;            /* 属性数据大小 */
    size_t objectSize;           /* 对象大小（位） */
    size_t maxObjectSize;        /* 最大对象大小（位） */
    uint32_t objectUsage;        /* 对象使用权限 */
    uint32_t objectType;         /* 对象类型 */
    uint32_t have_attrs;         /* 属性位字段 */

    /* Trusty 扩展字段 */
    uint32_t flags;              /* 访问标志 */
    uint32_t data_size;          /* 用户数据大小 */
    uint32_t data_offset;        /* 用户数据偏移 */
    uint8_t obj_id_hash[32];     /* 对象 ID 的 SHA-256 哈希 */
    uint8_t ta_uuid[16];         /* TA UUID */
    uint64_t create_time;        /* 创建时间戳 */
    uint64_t modify_time;        /* 修改时间戳 */
    uint32_t checksum;           /* 头部校验和 */
    uint32_t reserved[4];        /* 保留字段 */
};

#define TRUSTY_GP_OBJECT_MAGIC 0x54475054  /* 'TGPT' */
#define TRUSTY_GP_OBJECT_VERSION 1
```

#### 4.1.3 文件布局结构

```
+---------------------------+
|  trusty_gp_object_header  |  <- 对象头部（128 字节）
+---------------------------+
|     Attribute Data        |  <- 属性数据（变长）
|     (serialized)          |
+---------------------------+
|      User Data            |  <- 用户数据（变长）
|                           |
+---------------------------+
```

### 4.2 属性序列化格式

#### 4.2.1 属性数据布局
```c
/* 属性数据头部 */
struct trusty_gp_attr_header {
    uint32_t attr_count;        /* 属性数量 */
    uint32_t total_size;        /* 属性数据总大小 */
    uint32_t checksum;          /* 属性数据校验和 */
    uint32_t reserved;          /* 保留字段 */
};

/* 单个属性条目 */
struct trusty_gp_attr_entry {
    uint32_t attr_id;           /* 属性 ID */
    uint32_t attr_type;         /* 属性类型（REF/VALUE） */
    uint32_t attr_size;         /* 属性数据大小 */
    uint32_t attr_offset;       /* 属性数据偏移 */
};
```

#### 4.2.2 属性序列化实现
```c
/* 属性序列化 - 基于 OP-TEE 逻辑 */
TEE_Result trusty_serialize_attributes(const TEE_Attribute *attrs,
                                     uint32_t attr_count,
                                     void **attr_data,
                                     size_t *attr_size) {
    struct trusty_gp_attr_header *header;
    struct trusty_gp_attr_entry *entries;
    uint8_t *data_buf;
    size_t total_size, data_offset;
    uint32_t i;

    /* 计算总大小 */
    total_size = sizeof(*header) + attr_count * sizeof(*entries);
    for (i = 0; i < attr_count; i++) {
        if (attrs[i].attributeID & TEE_ATTR_FLAG_VALUE) {
            total_size += 8;  /* VALUE 属性：两个 uint32_t */
        } else {
            total_size += attrs[i].content.ref.length;  /* REF 属性：实际数据 */
        }
    }

    /* 分配缓冲区 */
    *attr_data = malloc(total_size);
    if (!*attr_data)
        return TEE_ERROR_OUT_OF_MEMORY;

    /* 填充头部 */
    header = (struct trusty_gp_attr_header *)*attr_data;
    header->attr_count = attr_count;
    header->total_size = total_size;
    header->checksum = 0;  /* 稍后计算 */
    header->reserved = 0;

    /* 填充属性条目 */
    entries = (struct trusty_gp_attr_entry *)(header + 1);
    data_buf = (uint8_t *)(entries + attr_count);
    data_offset = sizeof(*header) + attr_count * sizeof(*entries);

    for (i = 0; i < attr_count; i++) {
        entries[i].attr_id = attrs[i].attributeID;
        entries[i].attr_offset = data_offset;

        if (attrs[i].attributeID & TEE_ATTR_FLAG_VALUE) {
            /* VALUE 属性 */
            entries[i].attr_type = TRUSTY_GP_ATTR_TYPE_VALUE;
            entries[i].attr_size = 8;
            *(uint32_t *)(data_buf + data_offset - sizeof(*header) - attr_count * sizeof(*entries)) = attrs[i].content.value.a;
            *(uint32_t *)(data_buf + data_offset - sizeof(*header) - attr_count * sizeof(*entries) + 4) = attrs[i].content.value.b;
            data_offset += 8;
        } else {
            /* REF 属性 */
            entries[i].attr_type = TRUSTY_GP_ATTR_TYPE_REF;
            entries[i].attr_size = attrs[i].content.ref.length;
            memcpy(data_buf + data_offset - sizeof(*header) - attr_count * sizeof(*entries),
                   attrs[i].content.ref.buffer, attrs[i].content.ref.length);
            data_offset += attrs[i].content.ref.length;
        }
    }

    /* 计算校验和 */
    header->checksum = trusty_calculate_checksum((uint8_t *)*attr_data + sizeof(header->checksum),
                                               total_size - sizeof(header->checksum));

    *attr_size = total_size;
    return TEE_SUCCESS;
}
```

## 5. GP API 实现架构

### 5.1 API 分层实现架构

#### 5.1.1 四层架构设计
```mermaid
graph TB
    subgraph "Layer 1: GP API Interface"
        L1A[TEE_OpenPersistentObject]
        L1B[TEE_CreatePersistentObject]
        L1C[TEE_AllocateTransientObject]
        L1D[TEE_ReadObjectData]
    end

    subgraph "Layer 2: Object Management"
        L2A[trusty_obj_alloc/free]
        L2B[trusty_pobj_find/create]
        L2C[trusty_obj_set_busy/clear]
        L2D[trusty_attr_serialize/parse]
    end

    subgraph "Layer 3: Storage Operations"
        L3A[trusty_storage_ops]
        L3B[trusty_file_create/open]
        L3C[trusty_file_read/write]
        L3D[trusty_path_build]
    end

    subgraph "Layer 4: Trusty Storage Backend"
        L4A[storage_open_file]
        L4B[storage_read/write]
        L4C[storage_delete_file]
        L4D[storage_move_file]
    end

    L1A --> L2A
    L1B --> L2B
    L1C --> L2C
    L1D --> L2D

    L2A --> L3A
    L2B --> L3B
    L2C --> L3C
    L2D --> L3D

    L3A --> L4A
    L3B --> L4B
    L3C --> L4C
    L3D --> L4D
```

#### 5.1.2 核心 API 实现框架

**持久对象打开实现：**
```c
/* TEE_OpenPersistentObject 实现 - 基于 OP-TEE 逻辑 */
TEE_Result TEE_OpenPersistentObject(uint32_t storageID,
                                   const void *objectID,
                                   uint32_t objectIDLen,
                                   uint32_t flags,
                                   TEE_ObjectHandle *object) {
    struct trusty_tee_obj *obj = NULL;
    struct trusty_pobj *pobj = NULL;
    TEE_Result res;
    char storage_path[256];

    /* 参数验证 - 基于 OP-TEE 验证逻辑 */
    if (!objectID || objectIDLen == 0 || !object)
        return TEE_ERROR_BAD_PARAMETERS;

    /* 构造存储路径 */
    res = trusty_build_storage_path(storageID, objectID, objectIDLen, storage_path);
    if (res != TEE_SUCCESS)
        return res;

    /* 查找或创建持久对象 - 基于 OP-TEE 逻辑 */
    pobj = trusty_pobj_find_or_create(objectID, objectIDLen, storage_path);
    if (!pobj)
        return TEE_ERROR_OUT_OF_MEMORY;

    /* 检查访问冲突 - 基于 OP-TEE 逻辑 */
    res = trusty_pobj_check_access_conflict(pobj, flags);
    if (res != TEE_SUCCESS) {
        trusty_pobj_put(pobj);
        return res;
    }

    /* 检查对象是否存在 */
    bool exists;
    res = pobj->fops->file_exists(pobj, &exists);
    if (res != TEE_SUCCESS || !exists) {
        trusty_pobj_put(pobj);
        return TEE_ERROR_ITEM_NOT_FOUND;
    }

    /* 分配对象句柄 - 基于 OP-TEE 逻辑 */
    obj = trusty_obj_alloc();
    if (!obj) {
        trusty_pobj_put(pobj);
        return TEE_ERROR_OUT_OF_MEMORY;
    }

    /* 打开文件 */
    size_t file_size;
    res = pobj->fops->open(pobj, &file_size, &obj->fh);
    if (res != TEE_SUCCESS) {
        trusty_obj_free(obj);
        trusty_pobj_put(pobj);
        return res;
    }

    /* 加载对象信息和属性 - 基于 OP-TEE 逻辑 */
    res = trusty_load_object_info(obj, pobj);
    if (res != TEE_SUCCESS) {
        pobj->fops->close(&obj->fh);
        trusty_obj_free(obj);
        trusty_pobj_put(pobj);
        return res;
    }

    /* 设置对象状态 */
    obj->pobj = pobj;
    obj->info.handleFlags = flags;

    *object = (TEE_ObjectHandle)obj;
    return TEE_SUCCESS;
}
```

**瞬态对象分配实现：**
```c
/* TEE_AllocateTransientObject 实现 - 基于 OP-TEE 逻辑 */
TEE_Result TEE_AllocateTransientObject(uint32_t objectType,
                                      uint32_t maxObjectSize,
                                      TEE_ObjectHandle *object) {
    struct trusty_tee_obj *obj;

    /* 参数验证 - 基于 OP-TEE 验证逻辑 */
    if (!object)
        return TEE_ERROR_BAD_PARAMETERS;

    if (!trusty_is_valid_object_type(objectType))
        return TEE_ERROR_NOT_SUPPORTED;

    if (!trusty_is_valid_object_size(objectType, maxObjectSize))
        return TEE_ERROR_BAD_PARAMETERS;

    /* 分配对象句柄 - 基于 OP-TEE 逻辑 */
    obj = trusty_obj_alloc();
    if (!obj)
        return TEE_ERROR_OUT_OF_MEMORY;

    /* 初始化瞬态对象信息 - 基于 OP-TEE 逻辑 */
    obj->info.objectType = objectType;
    obj->info.maxObjectSize = maxObjectSize;
    obj->info.objectSize = 0;
    obj->info.objectUsage = 0xFFFFFFFF;  /* 所有用途 */
    obj->info.dataSize = 0;
    obj->info.dataPosition = 0;
    obj->info.handleFlags = 0;

    /* 瞬态对象不关联持久对象 */
    obj->pobj = NULL;
    obj->fh = INVALID_FILE_HANDLE;

    *object = (TEE_ObjectHandle)obj;
    return TEE_SUCCESS;
}
```

### 5.2 错误处理和转换机制

#### 5.2.1 错误码映射表
```c
/* Trusty 存储错误码到 GP 错误码的映射 */
TEE_Result trusty_convert_storage_error(int storage_err) {
    switch (storage_err) {
        case NO_ERROR:
            return TEE_SUCCESS;
        case ERR_NOT_FOUND:
            return TEE_ERROR_ITEM_NOT_FOUND;
        case ERR_ACCESS_DENIED:
            return TEE_ERROR_ACCESS_DENIED;
        case ERR_ALREADY_EXISTS:
            return TEE_ERROR_ACCESS_CONFLICT;
        case ERR_NO_MEMORY:
            return TEE_ERROR_OUT_OF_MEMORY;
        case ERR_NOT_VALID:
            return TEE_ERROR_BAD_PARAMETERS;
        case ERR_IO:
            return TEE_ERROR_STORAGE_NOT_AVAILABLE;
        case ERR_NOT_ENOUGH_BUFFER:
            return TEE_ERROR_SHORT_BUFFER;
        case ERR_BUSY:
            return TEE_ERROR_BUSY;
        default:
            return TEE_ERROR_GENERIC;
    }
}
```

#### 5.2.2 统一错误处理机制
```c
/* 统一的错误处理和资源清理 */
#define TRUSTY_GP_ERROR_CLEANUP(obj, pobj, fh, result) do { \
    if (fh != INVALID_FILE_HANDLE) { \
        storage_close_file(fh); \
    } \
    if (obj) { \
        trusty_obj_free(obj); \
    } \
    if (pobj) { \
        trusty_pobj_put(pobj); \
    } \
    return (result); \
} while (0)
```

## 6. 性能优化设计

### 6.1 对象缓存机制

#### 6.1.1 对象池化设计
```c
/* 对象池管理 - 基于 OP-TEE 优化思想 */
struct trusty_obj_pool {
    struct trusty_tee_obj **free_objects;  /* 空闲对象数组 */
    uint32_t pool_size;                    /* 池大小 */
    uint32_t free_count;                   /* 空闲对象数量 */
    mutex_t pool_lock;                     /* 池锁 */
};

static struct trusty_obj_pool obj_pool = {
    .pool_size = 64,
    .free_count = 0,
    .pool_lock = MUTEX_INITIAL_VALUE(obj_pool.pool_lock),
};

/* 从对象池分配对象 */
struct trusty_tee_obj *trusty_obj_pool_alloc(void) {
    struct trusty_tee_obj *obj = NULL;

    mutex_acquire(&obj_pool.pool_lock);
    if (obj_pool.free_count > 0) {
        obj = obj_pool.free_objects[--obj_pool.free_count];
        /* 重置对象状态 - 基于 OP-TEE 重置逻辑 */
        memset(&obj->info, 0, sizeof(obj->info));
        obj->busy = false;
        obj->have_attrs = 0;
        obj->attr = NULL;
        obj->ds_pos = 0;
        obj->pobj = NULL;
        obj->fh = INVALID_FILE_HANDLE;
    }
    mutex_release(&obj_pool.pool_lock);

    if (!obj) {
        obj = calloc(1, sizeof(*obj));
        if (obj) {
            mutex_init(&obj->obj_lock);
        }
    }

    return obj;
}
```

#### 6.1.2 属性缓存优化
```c
/* 属性缓存机制 - 基于 OP-TEE have_attrs 优化 */
struct trusty_attr_cache {
    uint32_t have_attrs;        /* 缓存的属性位字段 */
    void *cached_attrs;         /* 缓存的属性数据 */
    size_t cached_size;         /* 缓存数据大小 */
    bool dirty;                 /* 脏标志 */
};

/* 基于 have_attrs 的属性延迟加载 */
TEE_Result trusty_load_attributes_lazy(struct trusty_tee_obj *obj,
                                     uint32_t requested_attrs) {
    uint32_t missing_attrs = requested_attrs & ~obj->have_attrs;

    if (missing_attrs == 0) {
        return TEE_SUCCESS;  /* 所有请求的属性都已缓存 */
    }

    /* 只加载缺失的属性 */
    return trusty_load_specific_attributes(obj, missing_attrs);
}
```

### 6.2 TIPC 通信优化

#### 6.2.1 批量操作支持
```c
/* 批量属性操作 - 减少 TIPC 通信次数 */
TEE_Result trusty_batch_get_attributes(TEE_ObjectHandle object,
                                     const uint32_t *attr_ids,
                                     uint32_t attr_count,
                                     TEE_Attribute *attrs) {
    struct trusty_tee_obj *obj = (struct trusty_tee_obj *)object;
    uint32_t missing_mask = 0;
    uint32_t i;

    /* 检查哪些属性需要从存储加载 */
    for (i = 0; i < attr_count; i++) {
        if (!(obj->have_attrs & (1U << (attr_ids[i] & 0x1F)))) {
            missing_mask |= (1U << (attr_ids[i] & 0x1F));
        }
    }

    /* 批量加载缺失的属性 */
    if (missing_mask != 0) {
        TEE_Result res = trusty_load_attributes_batch(obj, missing_mask);
        if (res != TEE_SUCCESS)
            return res;
    }

    /* 从缓存中提取属性 */
    return trusty_extract_cached_attributes(obj, attr_ids, attr_count, attrs);
}
```

#### 6.2.2 消息压缩优化
```c
/* 基于 have_attrs 的属性压缩传输 */
TEE_Result trusty_compress_attributes(const TEE_Attribute *attrs,
                                    uint32_t attr_count,
                                    uint32_t have_attrs,
                                    void **compressed_data,
                                    size_t *compressed_size) {
    /* 只传输 have_attrs 位字段标识的属性 */
    uint32_t actual_count = 0;
    uint32_t i;

    for (i = 0; i < attr_count; i++) {
        if (have_attrs & (1U << (attrs[i].attributeID & 0x1F))) {
            actual_count++;
        }
    }

    /* 分配压缩缓冲区，只包含实际需要的属性 */
    return trusty_serialize_selected_attributes(attrs, attr_count,
                                              have_attrs, actual_count,
                                              compressed_data, compressed_size);
}
```

## 7. 安全机制设计

### 7.1 TA 隔离和访问控制

#### 7.1.1 基于 UUID 的 TA 隔离
```c
/* TA 隔离验证 - 基于 OP-TEE 逻辑 */
TEE_Result trusty_verify_ta_access(struct trusty_pobj *pobj,
                                 const struct uuid *ta_uuid) {
    /* 检查对象是否属于当前 TA */
    if (memcmp(&pobj->uuid, ta_uuid, sizeof(struct uuid)) != 0) {
        return TEE_ERROR_ACCESS_DENIED;
    }
    return TEE_SUCCESS;
}

/* 对象路径构建 - 包含 TA UUID 隔离 */
TEE_Result trusty_build_storage_path(uint32_t storage_id,
                                   const void *obj_id,
                                   uint32_t obj_id_len,
                                   const struct uuid *ta_uuid,
                                   char *path,
                                   size_t path_size) {
    char uuid_str[37];  /* UUID 字符串格式 */
    char obj_id_hex[129];  /* 对象 ID 十六进制格式 */

    /* 转换 UUID 为字符串 */
    snprintf(uuid_str, sizeof(uuid_str),
             "%08x-%04x-%04x-%02x%02x-%02x%02x%02x%02x%02x%02x",
             ta_uuid->time_low, ta_uuid->time_mid, ta_uuid->time_hi_and_version,
             ta_uuid->clock_seq_hi_and_reserved, ta_uuid->clock_seq_low,
             ta_uuid->node[0], ta_uuid->node[1], ta_uuid->node[2],
             ta_uuid->node[3], ta_uuid->node[4], ta_uuid->node[5]);

    /* 转换对象 ID 为十六进制 */
    trusty_bin_to_hex(obj_id, obj_id_len, obj_id_hex, sizeof(obj_id_hex));

    /* 构建分层路径：storage_type/ta_uuid/object_id */
    snprintf(path, path_size, "%s/%s/%s",
             trusty_get_storage_type_name(storage_id),
             uuid_str, obj_id_hex);

    return TEE_SUCCESS;
}
```

#### 7.1.2 对象访问权限控制
```c
/* 访问权限验证 - 基于 OP-TEE 逻辑 */
TEE_Result trusty_check_object_access(struct trusty_tee_obj *obj,
                                    uint32_t requested_access) {
    uint32_t allowed_access = obj->info.handleFlags;

    /* 检查读权限 */
    if ((requested_access & TEE_DATA_FLAG_ACCESS_READ) &&
        !(allowed_access & TEE_DATA_FLAG_ACCESS_READ)) {
        return TEE_ERROR_ACCESS_DENIED;
    }

    /* 检查写权限 */
    if ((requested_access & TEE_DATA_FLAG_ACCESS_WRITE) &&
        !(allowed_access & TEE_DATA_FLAG_ACCESS_WRITE)) {
        return TEE_ERROR_ACCESS_DENIED;
    }

    /* 检查元数据写权限 */
    if ((requested_access & TEE_DATA_FLAG_ACCESS_WRITE_META) &&
        !(allowed_access & TEE_DATA_FLAG_ACCESS_WRITE_META)) {
        return TEE_ERROR_ACCESS_DENIED;
    }

    return TEE_SUCCESS;
}

/* 对象使用权限限制 - 基于 OP-TEE 逻辑 */
TEE_Result trusty_restrict_object_usage(struct trusty_tee_obj *obj,
                                      uint32_t new_usage) {
    /* 只能减少权限，不能增加权限 */
    if ((new_usage & ~obj->info.objectUsage) != 0) {
        return TEE_ERROR_BAD_PARAMETERS;
    }

    /* 检查对象是否忙碌 */
    TEE_Result res = trusty_obj_set_busy(obj);
    if (res != TEE_SUCCESS) {
        return res;
    }

    /* 更新使用权限 */
    obj->info.objectUsage = new_usage;

    trusty_obj_clear_busy(obj);
    return TEE_SUCCESS;
}
```

### 7.2 数据完整性保护

#### 7.2.1 对象头部完整性验证
```c
/* 对象头部校验和计算 */
uint32_t trusty_calculate_header_checksum(const struct trusty_gp_object_header *header) {
    uint32_t checksum = 0;
    const uint32_t *data = (const uint32_t *)header;
    size_t words = (sizeof(*header) - sizeof(header->checksum)) / sizeof(uint32_t);

    for (size_t i = 0; i < words; i++) {
        checksum ^= data[i];
    }

    return checksum;
}

/* 对象头部完整性验证 */
TEE_Result trusty_verify_object_header(const struct trusty_gp_object_header *header) {
    /* 检查魔数 */
    if (header->magic != TRUSTY_GP_OBJECT_MAGIC) {
        return TEE_ERROR_BAD_FORMAT;
    }

    /* 检查版本 */
    if (header->version != TRUSTY_GP_OBJECT_VERSION) {
        return TEE_ERROR_NOT_SUPPORTED;
    }

    /* 验证校验和 */
    uint32_t calculated_checksum = trusty_calculate_header_checksum(header);
    if (calculated_checksum != header->checksum) {
        return TEE_ERROR_CORRUPTED_OBJECT;
    }

    /* 验证大小字段的一致性 */
    if (header->header_size != sizeof(*header) ||
        header->total_size < header->header_size + header->attr_size + header->data_size) {
        return TEE_ERROR_BAD_FORMAT;
    }

    return TEE_SUCCESS;
}
```

#### 7.2.2 属性数据完整性保护
```c
/* 属性数据完整性验证 */
TEE_Result trusty_verify_attributes_integrity(const void *attr_data,
                                            size_t attr_size) {
    const struct trusty_gp_attr_header *header = attr_data;

    if (attr_size < sizeof(*header)) {
        return TEE_ERROR_BAD_FORMAT;
    }

    /* 验证属性数据校验和 */
    uint32_t calculated_checksum = trusty_calculate_checksum(
        (const uint8_t *)attr_data + sizeof(header->checksum),
        attr_size - sizeof(header->checksum));

    if (calculated_checksum != header->checksum) {
        return TEE_ERROR_CORRUPTED_OBJECT;
    }

    /* 验证属性条目的一致性 */
    const struct trusty_gp_attr_entry *entries =
        (const struct trusty_gp_attr_entry *)(header + 1);

    for (uint32_t i = 0; i < header->attr_count; i++) {
        if (entries[i].attr_offset + entries[i].attr_size > attr_size) {
            return TEE_ERROR_BAD_FORMAT;
        }
    }

    return TEE_SUCCESS;
}
```

### 7.3 安全清除机制

#### 7.3.1 敏感数据安全清除
```c
/* 安全清除内存 - 防止编译器优化 */
void trusty_secure_memzero(void *ptr, size_t size) {
    volatile uint8_t *p = (volatile uint8_t *)ptr;
    while (size--) {
        *p++ = 0;
    }
}

/* 对象安全清除 - 基于 OP-TEE 逻辑 */
void trusty_obj_secure_cleanup(struct trusty_tee_obj *obj) {
    if (!obj)
        return;

    /* 清除敏感的对象信息 */
    trusty_secure_memzero(&obj->info, sizeof(obj->info));

    /* 清除属性数据 */
    if (obj->attr) {
        trusty_secure_memzero(obj->attr,
                            trusty_get_attr_size_from_have_attrs(obj->have_attrs));
        free(obj->attr);
        obj->attr = NULL;
    }

    /* 重置状态字段 */
    obj->have_attrs = 0;
    obj->ds_pos = 0;
    obj->busy = false;
}

/* 持久对象安全删除 */
TEE_Result trusty_pobj_secure_delete(struct trusty_pobj *pobj) {
    TEE_Result res;

    /* 安全覆写文件内容 */
    res = trusty_secure_overwrite_file(pobj);
    if (res != TEE_SUCCESS) {
        return res;
    }

    /* 删除文件 */
    res = pobj->fops->remove(pobj);
    if (res != TEE_SUCCESS) {
        return res;
    }

    /* 清除内存中的对象信息 */
    trusty_secure_memzero(pobj->obj_id, pobj->obj_id_len);
    trusty_secure_memzero(pobj->storage_path, strlen(pobj->storage_path));

    return TEE_SUCCESS;
}
```

## 8. 实现指导和最佳实践

### 8.1 开发阶段规划

#### 8.1.1 第一阶段：基础框架（4-6 周）
**目标**：建立基础的双层对象模型和核心数据结构

**任务清单**：
1. 实现 `trusty_tee_obj` 和 `trusty_pobj` 数据结构
2. 实现基础的对象分配和释放机制
3. 实现 OP-TEE 兼容的并发控制机制
4. 实现基础的存储路径构建和 TA 隔离
5. 建立单元测试框架

**验收标准**：
- 对象分配和释放无内存泄漏
- 并发控制机制正常工作
- TA 隔离机制有效
- 通过基础单元测试

#### 8.1.2 第二阶段：持久对象功能（6-8 周）
**目标**：实现完整的持久对象管理功能

**任务清单**：
1. 实现 GP 对象文件格式和序列化机制
2. 实现 `TEE_OpenPersistentObject` 和 `TEE_CreatePersistentObject`
3. 实现 `TEE_CloseAndDeletePersistentObject1` 和 `TEE_RenamePersistentObject`
4. 实现对象信息查询功能（`TEE_GetObjectInfo1`）
5. 实现基础的数据流操作

**验收标准**：
- 持久对象的完整生命周期管理
- 对象文件格式兼容性
- 数据完整性保护
- 通过持久对象功能测试

#### 8.1.3 第三阶段：属性系统（4-6 周）
**目标**：实现完整的对象属性管理系统

**任务清单**：
1. 实现属性序列化和反序列化机制
2. 实现 `TEE_GetObjectBufferAttribute` 和 `TEE_GetObjectValueAttribute`
3. 实现属性的缓存和延迟加载机制
4. 实现属性完整性验证
5. 优化属性操作性能

**验收标准**：
- 属性数据的正确序列化和存储
- 属性访问性能满足要求
- 属性完整性验证有效
- 通过属性系统测试

#### 8.1.4 第四阶段：瞬态对象和高级功能（6-8 周）
**目标**：实现瞬态对象系统和剩余的 GP API

**任务清单**：
1. 实现瞬态对象的内存管理
2. 实现瞬态对象的属性操作
3. 实现对象枚举功能
4. 实现密钥生成功能
5. 实现性能优化和缓存机制

**验收标准**：
- 瞬态对象的完整功能
- 所有 26 个 GP API 实现完成
- 性能指标满足要求
- 通过完整的兼容性测试

### 8.2 代码组织结构

#### 8.2.1 推荐的文件组织
```
trusty_gp_storage/
├── include/
│   ├── trusty_gp_storage.h          # 主要公共接口
│   ├── trusty_gp_objects.h          # 对象管理接口
│   ├── trusty_gp_attributes.h       # 属性管理接口
│   └── trusty_gp_internal.h         # 内部接口定义
├── src/
│   ├── gp_api/
│   │   ├── gp_generic_objects.c     # 通用对象函数实现
│   │   ├── gp_transient_objects.c   # 瞬态对象函数实现
│   │   ├── gp_persistent_objects.c  # 持久对象函数实现
│   │   ├── gp_enumeration.c         # 枚举函数实现
│   │   ├── gp_data_stream.c         # 数据流函数实现
│   │   └── gp_key_generation.c      # 密钥生成函数实现
│   ├── core/
│   │   ├── trusty_objects.c         # 对象管理核心实现
│   │   ├── trusty_pobjects.c        # 持久对象管理实现
│   │   ├── trusty_attributes.c      # 属性管理实现
│   │   ├── trusty_storage_ops.c     # 存储操作实现
│   │   └── trusty_concurrency.c     # 并发控制实现
│   ├── utils/
│   │   ├── trusty_serialization.c   # 序列化工具
│   │   ├── trusty_path_utils.c      # 路径工具
│   │   ├── trusty_error_convert.c   # 错误转换工具
│   │   └── trusty_security_utils.c  # 安全工具
│   └── backend/
│       ├── trusty_storage_backend.c # 存储后端适配
│       └── trusty_file_format.c     # 文件格式处理
├── tests/
│   ├── unit/                        # 单元测试
│   ├── integration/                 # 集成测试
│   └── compatibility/               # 兼容性测试
└── docs/
    ├── api_reference.md             # API 参考文档
    ├── implementation_guide.md      # 实现指南
    └── performance_tuning.md        # 性能调优指南
```

#### 8.2.2 编码规范建议
1. **命名约定**：
   - 公共 API：`TEE_` 前缀（GP 标准）
   - 内部函数：`trusty_` 前缀
   - 数据结构：`trusty_` 前缀
   - 常量：`TRUSTY_GP_` 前缀

2. **错误处理**：
   - 统一使用 `TEE_Result` 返回值
   - 实现统一的错误转换机制
   - 提供详细的错误日志

3. **内存管理**：
   - 严格的内存分配和释放配对
   - 使用对象池优化性能
   - 实现安全的内存清除

4. **并发安全**：
   - 遵循 OP-TEE 的锁顺序
   - 避免死锁和竞态条件
   - 实现超时机制

### 8.3 测试策略

#### 8.3.1 单元测试
- **对象管理测试**：对象分配、释放、生命周期
- **并发控制测试**：多线程访问、锁机制
- **属性系统测试**：序列化、反序列化、完整性
- **错误处理测试**：各种错误场景的处理

#### 8.3.2 集成测试
- **存储后端集成**：与 Trusty 存储服务的集成
- **TA 隔离测试**：多 TA 环境下的隔离验证
- **性能测试**：各种负载下的性能表现
- **兼容性测试**：与现有应用的兼容性

#### 8.3.3 压力测试
- **大量对象测试**：创建和管理大量对象
- **长时间运行测试**：长期稳定性验证
- **内存压力测试**：内存不足情况下的行为
- **并发压力测试**：高并发访问下的稳定性

## 9. 总结

### 9.1 设计优势
1. **成熟性保证**：完全基于 OP-TEE 验证的双层对象模型
2. **GP 标准兼容**：严格遵循 GP TEE Internal Core API v1.3.1 规范
3. **Trusty 适配**：完美集成 Trusty TEE 现有存储基础设施
4. **性能优化**：多层次的缓存和优化机制
5. **安全可靠**：完善的安全机制和数据保护

### 9.2 技术创新
1. **双层对象模型适配**：将 OP-TEE 成熟架构适配到 Trusty TEE
2. **属性压缩传输**：基于 have_attrs 的属性优化传输
3. **对象池化管理**：高效的对象内存管理机制
4. **分层存储格式**：向后兼容的 GP 对象存储格式

### 9.3 实施建议
1. **分阶段实施**：按照 4 个阶段逐步实现，降低风险
2. **充分测试**：建立完善的测试体系，确保质量
3. **性能监控**：实时监控性能指标，及时优化
4. **文档完善**：提供详细的 API 文档和实现指南

---

**设计状态**：✅ 任务四完成
**输出文档**：`docs/optee-based-design-specification.md`
**下一步**：开始任务五 - 实现路径规划和技术准备
