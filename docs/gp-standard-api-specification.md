# GP TEE Internal Core API v1.3.1 存储规范详细梳理

## 1. 引言

### 1.1 文档目的
本文档详细梳理 GP TEE Internal Core API v1.3.1 中的所有存储相关 API 规范，为 Trusty TEE 实现 GP 标准存储功能提供完整的技术参考。

### 1.2 GP 存储 API 概览
GP TEE Internal Core API v1.3.1 定义了 26 个存储相关 API，按功能分为 5 个类别：

| 分类 | API 数量 | 功能描述 |
|------|---------|----------|
| **Generic Object Functions** | 5个 | 通用对象操作：信息查询、权限控制、属性访问 |
| **Transient Object Functions** | 7个 | 瞬态对象管理：分配、释放、填充、属性操作 |
| **Persistent Object Functions** | 4个 | 持久对象管理：创建、打开、删除、重命名 |
| **Persistent Object Enumeration Functions** | 5个 | 持久对象枚举：枚举器管理、对象遍历 |
| **Data Stream Access Functions** | 4个 | 数据流操作：读写、定位、截断 |
| **Key Generation Function** | 1个 | 密钥生成：生成密钥对象 |

### 1.3 GP 存储架构核心概念

#### 1.3.1 对象类型分类
- **瞬态对象（Transient Objects）**：存储在内存中，TA 会话结束时自动销毁
- **持久对象（Persistent Objects）**：存储在非易失性存储中，跨会话持久保存

#### 1.3.2 存储类型定义
```c
#define TEE_STORAGE_PRIVATE    0x00000001  // TA 私有存储
#define TEE_STORAGE_PERSO      0x00000002  // 个人化存储
#define TEE_STORAGE_PROTECTED  0x00000003  // 受保护存储
```

#### 1.3.3 对象句柄系统
```c
typedef uint32_t TEE_ObjectHandle;
typedef uint32_t TEE_ObjectEnumHandle;

#define TEE_HANDLE_NULL 0x00000000
```

## 2. Generic Object Functions（通用对象函数 - 5个）

### 2.1 TEE_GetObjectInfo1 - 获取对象信息

#### 2.1.1 函数签名
```c
void TEE_GetObjectInfo1(TEE_ObjectHandle object, TEE_ObjectInfo *objectInfo);
```

#### 2.1.2 功能描述
获取对象的详细信息，包括对象类型、大小、使用权限、属性标志等。

#### 2.1.3 参数说明
- **object**：对象句柄，可以是瞬态对象或持久对象
- **objectInfo**：输出参数，指向 `TEE_ObjectInfo` 结构的指针

#### 2.1.4 TEE_ObjectInfo 结构定义
```c
typedef struct {
    uint32_t objectType;        // 对象类型
    uint32_t objectSize;        // 对象大小（位）
    uint32_t maxObjectSize;     // 最大对象大小（位）
    uint32_t objectUsage;       // 对象使用权限
    uint32_t dataSize;          // 数据流大小（字节）
    uint32_t dataPosition;      // 当前数据流位置
    uint32_t handleFlags;       // 句柄标志
} TEE_ObjectInfo;
```

#### 2.1.5 行为规范
- 如果 object 为 `TEE_HANDLE_NULL`，函数静默返回
- 如果 objectInfo 为 NULL，函数静默返回
- 函数总是成功执行，不返回错误码

### 2.2 TEE_RestrictObjectUsage1 - 限制对象使用权限

#### 2.2.1 函数签名
```c
TEE_Result TEE_RestrictObjectUsage1(TEE_ObjectHandle object, uint32_t objectUsage);
```

#### 2.2.2 功能描述
限制对象的使用权限，只能减少权限，不能增加权限。

#### 2.2.3 参数说明
- **object**：对象句柄
- **objectUsage**：新的使用权限（必须是当前权限的子集）

#### 2.2.4 返回值
- `TEE_SUCCESS`：操作成功
- `TEE_ERROR_BAD_PARAMETERS`：参数无效
- `TEE_ERROR_BUSY`：对象正在使用中

#### 2.2.5 使用权限标志
```c
#define TEE_USAGE_EXTRACTABLE     0x00000001
#define TEE_USAGE_ENCRYPT         0x00000002
#define TEE_USAGE_DECRYPT         0x00000004
#define TEE_USAGE_MAC             0x00000008
#define TEE_USAGE_SIGN            0x00000010
#define TEE_USAGE_VERIFY          0x00000020
#define TEE_USAGE_DERIVE          0x00000040
```

### 2.3 TEE_GetObjectBufferAttribute - 获取缓冲区属性

#### 2.3.1 函数签名
```c
TEE_Result TEE_GetObjectBufferAttribute(TEE_ObjectHandle object,
                                       uint32_t attributeID,
                                       void *buffer,
                                       uint32_t *size);
```

#### 2.3.2 功能描述
获取对象的缓冲区类型属性值。

#### 2.3.3 参数说明
- **object**：对象句柄
- **attributeID**：属性标识符
- **buffer**：输出缓冲区
- **size**：输入时为缓冲区大小，输出时为实际属性大小

#### 2.3.4 返回值
- `TEE_SUCCESS`：操作成功
- `TEE_ERROR_BAD_PARAMETERS`：参数无效
- `TEE_ERROR_ITEM_NOT_FOUND`：属性不存在
- `TEE_ERROR_SHORT_BUFFER`：缓冲区太小

### 2.4 TEE_GetObjectValueAttribute - 获取值属性

#### 2.4.1 函数签名
```c
TEE_Result TEE_GetObjectValueAttribute(TEE_ObjectHandle object,
                                      uint32_t attributeID,
                                      uint32_t *a,
                                      uint32_t *b);
```

#### 2.4.2 功能描述
获取对象的值类型属性。

#### 2.4.3 参数说明
- **object**：对象句柄
- **attributeID**：属性标识符
- **a**、**b**：输出参数，属性值的两个 32 位部分

### 2.5 TEE_CloseObject - 关闭对象

#### 2.5.1 函数签名
```c
void TEE_CloseObject(TEE_ObjectHandle object);
```

#### 2.5.2 功能描述
关闭对象句柄，释放相关资源。对于瞬态对象，这会销毁对象；对于持久对象，只是关闭句柄。

#### 2.5.3 行为规范
- 如果 object 为 `TEE_HANDLE_NULL`，函数静默返回
- 关闭后的句柄不能再使用
- 对于持久对象，数据仍然保存在存储中

## 3. Transient Object Functions（瞬态对象函数 - 7个）

### 3.1 TEE_AllocateTransientObject - 分配瞬态对象

#### 3.1.1 函数签名
```c
TEE_Result TEE_AllocateTransientObject(uint32_t objectType,
                                      uint32_t maxObjectSize,
                                      TEE_ObjectHandle *object);
```

#### 3.1.2 功能描述
分配一个新的瞬态对象，对象存储在内存中。

#### 3.1.3 参数说明
- **objectType**：对象类型（如 `TEE_TYPE_AES`、`TEE_TYPE_RSA_KEYPAIR` 等）
- **maxObjectSize**：最大对象大小（位）
- **object**：输出参数，新分配的对象句柄

#### 3.1.4 支持的对象类型
```c
#define TEE_TYPE_AES                0xA0000010
#define TEE_TYPE_DES                0xA0000011
#define TEE_TYPE_DES3               0xA0000013
#define TEE_TYPE_HMAC_MD5           0xA0000001
#define TEE_TYPE_HMAC_SHA1          0xA0000002
#define TEE_TYPE_HMAC_SHA224        0xA0000003
#define TEE_TYPE_HMAC_SHA256        0xA0000004
#define TEE_TYPE_HMAC_SHA384        0xA0000005
#define TEE_TYPE_HMAC_SHA512        0xA0000006
#define TEE_TYPE_RSA_PUBLIC_KEY     0xA0000030
#define TEE_TYPE_RSA_KEYPAIR        0xA1000030
#define TEE_TYPE_DSA_PUBLIC_KEY     0xA0000031
#define TEE_TYPE_DSA_KEYPAIR        0xA1000031
#define TEE_TYPE_DH_KEYPAIR         0xA1000032
#define TEE_TYPE_ECDSA_PUBLIC_KEY   0xA0000041
#define TEE_TYPE_ECDSA_KEYPAIR      0xA1000041
#define TEE_TYPE_ECDH_PUBLIC_KEY    0xA0000042
#define TEE_TYPE_ECDH_KEYPAIR       0xA1000042
#define TEE_TYPE_GENERIC_SECRET     0xA0000000
#define TEE_TYPE_DATA               0xA1000033
```

#### 3.1.5 返回值
- `TEE_SUCCESS`：操作成功
- `TEE_ERROR_BAD_PARAMETERS`：参数无效
- `TEE_ERROR_OUT_OF_MEMORY`：内存不足
- `TEE_ERROR_NOT_SUPPORTED`：不支持的对象类型

### 3.2 TEE_FreeTransientObject - 释放瞬态对象

#### 3.2.1 函数签名
```c
void TEE_FreeTransientObject(TEE_ObjectHandle object);
```

#### 3.2.2 功能描述
释放瞬态对象及其占用的所有资源。

#### 3.2.3 行为规范
- 如果 object 为 `TEE_HANDLE_NULL`，函数静默返回
- 如果 object 是持久对象句柄，函数静默返回
- 释放后的句柄不能再使用

### 3.3 TEE_ResetTransientObject - 重置瞬态对象

#### 3.3.1 函数签名
```c
void TEE_ResetTransientObject(TEE_ObjectHandle object);
```

#### 3.3.2 功能描述
将瞬态对象重置到初始状态，清除所有属性和数据。

### 3.4 TEE_PopulateTransientObject - 填充瞬态对象

#### 3.4.1 函数签名
```c
TEE_Result TEE_PopulateTransientObject(TEE_ObjectHandle object,
                                      const TEE_Attribute *attrs,
                                      uint32_t attrCount);
```

#### 3.4.2 功能描述
使用属性数组填充瞬态对象。

#### 3.4.3 TEE_Attribute 结构定义
```c
typedef struct {
    uint32_t attributeID;
    union {
        struct {
            void *buffer;
            uint32_t length;
        } ref;
        struct {
            uint32_t a;
            uint32_t b;
        } value;
    } content;
} TEE_Attribute;
```

### 3.5 TEE_InitRefAttribute - 初始化引用属性

#### 3.5.1 函数签名
```c
void TEE_InitRefAttribute(TEE_Attribute *attr,
                         uint32_t attributeID,
                         const void *buffer,
                         uint32_t length);
```

### 3.6 TEE_InitValueAttribute - 初始化值属性

#### 3.6.1 函数签名
```c
void TEE_InitValueAttribute(TEE_Attribute *attr,
                           uint32_t attributeID,
                           uint32_t a,
                           uint32_t b);
```

### 3.7 TEE_CopyObjectAttributes1 - 复制对象属性

#### 3.7.1 函数签名
```c
void TEE_CopyObjectAttributes1(TEE_ObjectHandle destObject,
                              TEE_ObjectHandle srcObject);
```

## 4. Persistent Object Functions（持久对象函数 - 4个）

### 4.1 TEE_OpenPersistentObject - 打开持久对象

#### 4.1.1 函数签名
```c
TEE_Result TEE_OpenPersistentObject(uint32_t storageID,
                                   const void *objectID,
                                   uint32_t objectIDLen,
                                   uint32_t flags,
                                   TEE_ObjectHandle *object);
```

#### 4.1.2 功能描述
打开一个已存在的持久对象。

#### 4.1.3 参数说明
- **storageID**：存储标识符（`TEE_STORAGE_PRIVATE` 等）
- **objectID**：对象标识符缓冲区
- **objectIDLen**：对象标识符长度
- **flags**：访问标志
- **object**：输出参数，对象句柄

#### 4.1.4 访问标志定义
```c
#define TEE_DATA_FLAG_ACCESS_READ       0x00000001
#define TEE_DATA_FLAG_ACCESS_WRITE      0x00000002
#define TEE_DATA_FLAG_ACCESS_WRITE_META 0x00000004
#define TEE_DATA_FLAG_SHARE_READ        0x00000010
#define TEE_DATA_FLAG_SHARE_WRITE       0x00000020
#define TEE_DATA_FLAG_OVERWRITE         0x00000400
```

#### 4.1.5 返回值
- `TEE_SUCCESS`：操作成功
- `TEE_ERROR_BAD_PARAMETERS`：参数无效
- `TEE_ERROR_ITEM_NOT_FOUND`：对象不存在
- `TEE_ERROR_ACCESS_DENIED`：访问被拒绝
- `TEE_ERROR_ACCESS_CONFLICT`：访问冲突
- `TEE_ERROR_OUT_OF_MEMORY`：内存不足
- `TEE_ERROR_STORAGE_NOT_AVAILABLE`：存储不可用

### 4.2 TEE_CreatePersistentObject - 创建持久对象

#### 4.2.1 函数签名
```c
TEE_Result TEE_CreatePersistentObject(uint32_t storageID,
                                     const void *objectID,
                                     uint32_t objectIDLen,
                                     uint32_t flags,
                                     TEE_ObjectHandle attributes,
                                     const void *initialData,
                                     uint32_t initialDataLen,
                                     TEE_ObjectHandle *object);
```

#### 4.2.2 功能描述
创建一个新的持久对象，可以同时设置初始属性和数据。

#### 4.2.3 参数说明
- **storageID**：存储标识符
- **objectID**：对象标识符缓冲区
- **objectIDLen**：对象标识符长度
- **flags**：创建标志
- **attributes**：属性对象句柄（可以为 `TEE_HANDLE_NULL`）
- **initialData**：初始数据缓冲区（可以为 NULL）
- **initialDataLen**：初始数据长度
- **object**：输出参数，新创建的对象句柄

#### 4.2.4 创建标志
```c
#define TEE_DATA_FLAG_ACCESS_READ       0x00000001
#define TEE_DATA_FLAG_ACCESS_WRITE      0x00000002
#define TEE_DATA_FLAG_ACCESS_WRITE_META 0x00000004
#define TEE_DATA_FLAG_EXCLUSIVE         0x00000400
```

### 4.3 TEE_CloseAndDeletePersistentObject1 - 关闭并删除持久对象

#### 4.3.1 函数签名
```c
TEE_Result TEE_CloseAndDeletePersistentObject1(TEE_ObjectHandle object);
```

#### 4.3.2 功能描述
关闭持久对象句柄并从存储中删除对象。

#### 4.3.3 返回值
- `TEE_SUCCESS`：操作成功
- `TEE_ERROR_BAD_PARAMETERS`：参数无效
- `TEE_ERROR_ACCESS_DENIED`：访问被拒绝
- `TEE_ERROR_STORAGE_NOT_AVAILABLE`：存储不可用

### 4.4 TEE_RenamePersistentObject - 重命名持久对象

#### 4.4.1 函数签名
```c
TEE_Result TEE_RenamePersistentObject(TEE_ObjectHandle object,
                                     const void *newObjectID,
                                     uint32_t newObjectIDLen);
```

#### 4.4.2 功能描述
重命名一个持久对象的标识符。

#### 4.4.3 参数说明
- **object**：对象句柄
- **newObjectID**：新的对象标识符
- **newObjectIDLen**：新标识符长度

## 5. Persistent Object Enumeration Functions（持久对象枚举函数 - 5个）

### 5.1 TEE_AllocatePersistentObjectEnumerator - 分配持久对象枚举器

#### 5.1.1 函数签名
```c
TEE_Result TEE_AllocatePersistentObjectEnumerator(TEE_ObjectEnumHandle *objectEnumerator);
```

#### 5.1.2 功能描述
分配一个持久对象枚举器，用于遍历存储中的对象。

#### 5.1.3 参数说明
- **objectEnumerator**：输出参数，枚举器句柄

#### 5.1.4 返回值
- `TEE_SUCCESS`：操作成功
- `TEE_ERROR_OUT_OF_MEMORY`：内存不足

### 5.2 TEE_FreePersistentObjectEnumerator - 释放持久对象枚举器

#### 5.2.1 函数签名
```c
void TEE_FreePersistentObjectEnumerator(TEE_ObjectEnumHandle objectEnumerator);
```

#### 5.2.2 功能描述
释放持久对象枚举器及其资源。

### 5.3 TEE_ResetPersistentObjectEnumerator - 重置持久对象枚举器

#### 5.3.1 函数签名
```c
void TEE_ResetPersistentObjectEnumerator(TEE_ObjectEnumHandle objectEnumerator);
```

#### 5.3.2 功能描述
将枚举器重置到初始状态，准备重新开始枚举。

### 5.4 TEE_StartPersistentObjectEnumerator - 启动持久对象枚举

#### 5.4.1 函数签名
```c
TEE_Result TEE_StartPersistentObjectEnumerator(TEE_ObjectEnumHandle objectEnumerator,
                                              uint32_t storageID);
```

#### 5.4.2 功能描述
启动对指定存储的持久对象枚举。

#### 5.4.3 参数说明
- **objectEnumerator**：枚举器句柄
- **storageID**：要枚举的存储标识符

#### 5.4.4 返回值
- `TEE_SUCCESS`：操作成功
- `TEE_ERROR_BAD_PARAMETERS`：参数无效
- `TEE_ERROR_ITEM_NOT_FOUND`：存储不存在

### 5.5 TEE_GetNextPersistentObject - 获取下一个持久对象

#### 5.5.1 函数签名
```c
TEE_Result TEE_GetNextPersistentObject(TEE_ObjectEnumHandle objectEnumerator,
                                      TEE_ObjectInfo *objectInfo,
                                      void *objectID,
                                      uint32_t *objectIDLen);
```

#### 5.5.2 功能描述
获取枚举中的下一个持久对象信息。

#### 5.5.3 参数说明
- **objectEnumerator**：枚举器句柄
- **objectInfo**：输出参数，对象信息
- **objectID**：输出缓冲区，对象标识符
- **objectIDLen**：输入时为缓冲区大小，输出时为实际标识符长度

#### 5.5.4 返回值
- `TEE_SUCCESS`：操作成功
- `TEE_ERROR_ITEM_NOT_FOUND`：没有更多对象
- `TEE_ERROR_SHORT_BUFFER`：缓冲区太小

## 6. Data Stream Access Functions（数据流访问函数 - 4个）

### 6.1 TEE_ReadObjectData - 读取对象数据

#### 6.1.1 函数签名
```c
TEE_Result TEE_ReadObjectData(TEE_ObjectHandle object,
                             void *buffer,
                             uint32_t size,
                             uint32_t *count);
```

#### 6.1.2 功能描述
从对象的当前数据流位置读取数据。

#### 6.1.3 参数说明
- **object**：对象句柄
- **buffer**：输出缓冲区
- **size**：要读取的字节数
- **count**：输出参数，实际读取的字节数

#### 6.1.4 返回值
- `TEE_SUCCESS`：操作成功
- `TEE_ERROR_BAD_PARAMETERS`：参数无效
- `TEE_ERROR_ACCESS_DENIED`：访问被拒绝

### 6.2 TEE_WriteObjectData - 写入对象数据

#### 6.2.1 函数签名
```c
TEE_Result TEE_WriteObjectData(TEE_ObjectHandle object,
                              const void *buffer,
                              uint32_t size);
```

#### 6.2.2 功能描述
向对象的当前数据流位置写入数据。

#### 6.2.3 参数说明
- **object**：对象句柄
- **buffer**：输入数据缓冲区
- **size**：要写入的字节数

### 6.3 TEE_TruncateObjectData - 截断对象数据

#### 6.3.1 函数签名
```c
TEE_Result TEE_TruncateObjectData(TEE_ObjectHandle object,
                                 uint32_t size);
```

#### 6.3.2 功能描述
将对象的数据流截断到指定大小。

#### 6.3.3 参数说明
- **object**：对象句柄
- **size**：新的数据流大小

### 6.4 TEE_SeekObjectData - 定位对象数据

#### 6.4.1 函数签名
```c
TEE_Result TEE_SeekObjectData(TEE_ObjectHandle object,
                             int32_t offset,
                             uint32_t whence);
```

#### 6.4.2 功能描述
设置对象数据流的当前位置。

#### 6.4.3 参数说明
- **object**：对象句柄
- **offset**：偏移量
- **whence**：定位方式

#### 6.4.4 定位方式定义
```c
#define TEE_DATA_SEEK_SET 0  // 从文件开始
#define TEE_DATA_SEEK_CUR 1  // 从当前位置
#define TEE_DATA_SEEK_END 2  // 从文件结束
```

## 7. Key Generation Function（密钥生成函数 - 1个）

### 7.1 TEE_GenerateKey - 生成密钥

#### 7.1.1 函数签名
```c
TEE_Result TEE_GenerateKey(TEE_ObjectHandle object,
                          uint32_t keySize,
                          const TEE_Attribute *params,
                          uint32_t paramCount);
```

#### 7.1.2 功能描述
为瞬态对象生成密钥材料。

#### 7.1.3 参数说明
- **object**：瞬态对象句柄
- **keySize**：密钥大小（位）
- **params**：生成参数数组
- **paramCount**：参数数量

## 8. GP 标准错误码定义

### 8.1 通用错误码
```c
#define TEE_SUCCESS                    0x00000000
#define TEE_ERROR_GENERIC              0xFFFF0000
#define TEE_ERROR_ACCESS_DENIED        0xFFFF0001
#define TEE_ERROR_CANCEL               0xFFFF0002
#define TEE_ERROR_ACCESS_CONFLICT      0xFFFF0003
#define TEE_ERROR_EXCESS_DATA          0xFFFF0004
#define TEE_ERROR_BAD_FORMAT           0xFFFF0005
#define TEE_ERROR_BAD_PARAMETERS       0xFFFF0006
#define TEE_ERROR_BAD_STATE            0xFFFF0007
#define TEE_ERROR_ITEM_NOT_FOUND       0xFFFF0008
#define TEE_ERROR_NOT_IMPLEMENTED      0xFFFF0009
#define TEE_ERROR_NOT_SUPPORTED        0xFFFF000A
#define TEE_ERROR_NO_DATA              0xFFFF000B
#define TEE_ERROR_OUT_OF_MEMORY        0xFFFF000C
#define TEE_ERROR_BUSY                 0xFFFF000D
#define TEE_ERROR_COMMUNICATION        0xFFFF000E
#define TEE_ERROR_SECURITY             0xFFFF000F
#define TEE_ERROR_SHORT_BUFFER         0xFFFF0010
#define TEE_ERROR_EXTERNAL_CANCEL      0xFFFF0011
#define TEE_ERROR_OVERFLOW             0xFFFF300F
#define TEE_ERROR_TARGET_DEAD          0xFFFF3024
#define TEE_ERROR_STORAGE_NOT_AVAILABLE 0xFFFF3025
#define TEE_ERROR_STORAGE_NO_SPACE     0xFFFF3041
```

### 8.2 存储特定错误码
- `TEE_ERROR_STORAGE_NOT_AVAILABLE`：存储服务不可用
- `TEE_ERROR_STORAGE_NO_SPACE`：存储空间不足
- `TEE_ERROR_ACCESS_CONFLICT`：对象访问冲突
- `TEE_ERROR_ITEM_NOT_FOUND`：对象或属性不存在

## 9. GP 标准数据结构总结

### 9.1 核心数据结构
```c
// 对象信息结构
typedef struct {
    uint32_t objectType;
    uint32_t objectSize;
    uint32_t maxObjectSize;
    uint32_t objectUsage;
    uint32_t dataSize;
    uint32_t dataPosition;
    uint32_t handleFlags;
} TEE_ObjectInfo;

// 属性结构
typedef struct {
    uint32_t attributeID;
    union {
        struct {
            void *buffer;
            uint32_t length;
        } ref;
        struct {
            uint32_t a;
            uint32_t b;
        } value;
    } content;
} TEE_Attribute;
```

### 9.2 句柄类型
```c
typedef uint32_t TEE_ObjectHandle;
typedef uint32_t TEE_ObjectEnumHandle;
typedef uint32_t TEE_Result;
```

## 10. 实现要求总结

### 10.1 必须实现的功能
1. **完整的 26 个 API**：所有 GP 标准存储 API 必须实现
2. **对象句柄管理**：支持瞬态和持久对象的句柄分配和管理
3. **属性系统**：完整的对象属性存储和访问机制
4. **存储类型支持**：支持三种 GP 标准存储类型
5. **错误处理**：完整的 GP 标准错误码支持

### 10.2 性能和安全要求
1. **TA 隔离**：不同 TA 的对象完全隔离
2. **并发安全**：支持多线程并发访问
3. **数据完整性**：保证存储数据的完整性和一致性
4. **访问控制**：严格的对象访问权限控制

---

**文档状态**：✅ 任务二完成
**输出文档**：`docs/gp-standard-api-specification.md`
**下一步**：开始任务三 - 现有实现与 GP 标准差距分析
