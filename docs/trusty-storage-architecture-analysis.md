# Trusty TEE 现有存储架构深度分析报告

## 1. 引言

### 1.1 分析目标
本报告对 Trusty TEE 现有存储系统进行全面深度分析，为实现 GP 标准可信存储功能提供技术基础。分析涵盖用户空间存储接口、存储服务实现、TIPC 通信机制和文件系统架构。

### 1.2 分析范围
- **用户空间存储库**：`user/base/lib/storage/` 中的存储接口实现
- **存储服务架构**：`user/app/storage/` 中的存储服务核心实现
- **接口协议定义**：`interface/storage/storage.h` 中的存储协议规范
- **TIPC 通信机制**：存储服务的进程间通信实现

### 1.3 分析方法
采用代码级别的深度分析，结合架构图表和数据流分析，全面梳理现有存储系统的设计和实现。

## 2. 用户空间存储接口分析

### 2.1 存储库接口概览

Trusty TEE 用户空间存储库位于 `user/base/lib/storage/`，提供了完整的存储操作接口：

#### 2.1.1 核心数据类型定义
```c
typedef handle_t storage_session_t;
typedef uint64_t file_handle_t;
typedef uint64_t storage_off_t;

#define STORAGE_INVALID_SESSION ((storage_session_t)INVALID_IPC_HANDLE)
#define STORAGE_MAX_NAME_LENGTH_BYTES 159
```

#### 2.1.2 存储端口类型
Trusty TEE 定义了 5 种不同安全级别的存储端口：

| 端口名称 | 安全特性 | 使用场景 |
|----------|----------|----------|
| `STORAGE_CLIENT_NSP_PORT` | 持久化，无回滚检测 | 一般持久化需求 |
| `STORAGE_CLIENT_TD_PORT` | 篡改和回滚检测 | 大多数应用推荐 |
| `STORAGE_CLIENT_TDP_PORT` | 篡改和回滚检测，设备擦除保护 | 关键数据保护 |
| `STORAGE_CLIENT_TDEA_PORT` | 早期启动阶段存储 | 启动前存储需求 |
| `STORAGE_CLIENT_TP_PORT` | 篡改防护存储 | 最高安全级别 |

#### 2.1.3 主要存储操作接口

**会话管理接口：**
```c
int storage_open_session(storage_session_t* session_p, const char* type);
void storage_close_session(storage_session_t session);
```

**文件操作接口：**
```c
int storage_open_file(storage_session_t session, file_handle_t* handle_p, 
                     const char* name, uint32_t flags, uint32_t opflags);
void storage_close_file(file_handle_t handle);
int storage_delete_file(storage_session_t session, const char* name, uint32_t opflags);
int storage_move_file(storage_session_t session, file_handle_t handle,
                     const char* old_name, const char* new_name, 
                     uint32_t flags, uint32_t opflags);
```

**数据读写接口：**
```c
ssize_t storage_read(file_handle_t handle, storage_off_t off, void* buf, size_t size);
ssize_t storage_write(file_handle_t fh, storage_off_t off, const void* buf, 
                     size_t size, uint32_t opflags);
int storage_set_file_size(file_handle_t handle, storage_off_t file_size, uint32_t opflags);
int storage_get_file_size(file_handle_t handle, storage_off_t* file_size);
```

**目录枚举接口：**
```c
int storage_open_dir(storage_session_t session, const char* path, 
                    struct storage_open_dir_state** state);
int storage_read_dir(storage_session_t session, struct storage_open_dir_state* state,
                    uint8_t* flags, char* name, size_t name_size);
void storage_close_dir(struct storage_open_dir_state* state);
```

### 2.2 存储操作标志和错误处理

#### 2.2.1 操作标志定义
```c
#define STORAGE_OP_COMPLETE 0x1U        // 强制提交当前事务
#define STORAGE_OP_CHECKPOINT 0x2U      // 检查点文件系统状态
#define STORAGE_OP_FS_REPAIRED_ACK 0x4U // 确认文件系统修复
```

#### 2.2.2 错误码映射
```c
enum storage_err {
    STORAGE_NO_ERROR = 0,
    STORAGE_ERR_GENERIC = 1,
    STORAGE_ERR_NOT_VALID = 2,
    STORAGE_ERR_UNIMPLEMENTED = 3,
    STORAGE_ERR_ACCESS = 4,
    STORAGE_ERR_NOT_FOUND = 5,
    STORAGE_ERR_EXIST = 6,
    STORAGE_ERR_TRANSACT = 7,
    STORAGE_ERR_SYNC_FAILURE = 8,
    STORAGE_ERR_NOT_ALLOWED = 9,
    STORAGE_ERR_CORRUPTED = 10,
    STORAGE_ERR_FS_REPAIRED = 11,
};
```

## 3. 存储服务架构分析

### 3.1 存储服务模块组织

存储服务位于 `user/app/storage/`，采用模块化设计：

#### 3.1.1 核心模块列表
| 模块文件 | 功能描述 | 关键数据结构 |
|----------|----------|--------------|
| `main.c` | 服务入口和初始化 | `ipc_port_context` |
| `ipc.c` | TIPC 通信框架 | `ipc_channel_context` |
| `proxy.c` | 存储代理服务 | `storage_session` |
| `client_tipc.c` | 客户端 TIPC 处理 | `client_session_tipc` |
| `fs.h` | 文件系统状态管理 | `struct fs` |
| `file.c` | 文件操作实现 | `file_handle` |
| `transaction.c` | 事务处理机制 | `struct transaction` |
| `block_*.c` | 块设备管理 | `block_device` |
| `crypt.c` | 加密和安全机制 | `struct key` |

#### 3.1.2 文件系统核心结构
```c
struct fs {
    struct list_node node;
    struct block_device* dev;
    struct list_node transactions;
    struct list_node allocated;
    struct block_set free;
    struct block_tree files;
    struct block_mac checkpoint;
    struct block_set checkpoint_free;
    struct block_device* super_dev;
    bool readable;
    bool writable;
    bool allow_tampering;
    const struct key* key;
    data_block_t super_block[2];
    unsigned int super_block_version;
    unsigned int written_super_block_version;
    bool main_repaired;
    bool alternate_data;
    bool needs_full_scan;
    bool checkpoint_required;
    struct super_block_backup backup;
    data_block_t min_block_num;
    size_t block_num_size;
    size_t mac_size;
    data_block_t reserved_count;
    struct transaction* initial_super_block_tr;
    const char* name;
};
```

### 3.2 TIPC 通信机制分析

#### 3.2.1 存储命令协议
```c
enum storage_cmd {
    STORAGE_FILE_DELETE = 1 << STORAGE_REQ_SHIFT,
    STORAGE_FILE_OPEN = 2 << STORAGE_REQ_SHIFT,
    STORAGE_FILE_CLOSE = 3 << STORAGE_REQ_SHIFT,
    STORAGE_FILE_READ = 4 << STORAGE_REQ_SHIFT,
    STORAGE_FILE_WRITE = 5 << STORAGE_REQ_SHIFT,
    STORAGE_FILE_GET_SIZE = 6 << STORAGE_REQ_SHIFT,
    STORAGE_FILE_SET_SIZE = 7 << STORAGE_REQ_SHIFT,
    STORAGE_RPMB_SEND = 8 << STORAGE_REQ_SHIFT,
    STORAGE_END_TRANSACTION = 9 << STORAGE_REQ_SHIFT,
    STORAGE_FILE_MOVE = 10 << STORAGE_REQ_SHIFT,
    STORAGE_FILE_LIST = 11 << STORAGE_REQ_SHIFT,
    STORAGE_FILE_GET_MAX_SIZE = 12 << STORAGE_REQ_SHIFT,
};
```

#### 3.2.2 消息格式定义
```c
struct storage_msg {
    uint32_t cmd;        // 命令类型
    uint32_t op_id;      // 操作标识符
    uint32_t flags;      // 操作标志
    uint32_t size;       // 消息总大小
    int32_t result;      // 操作结果
    uint32_t __reserved; // 保留字段
    uint8_t payload[0];  // 命令特定数据
};
```

#### 3.2.3 IPC 通信流程
```mermaid
sequenceDiagram
    participant Client as TA Client
    participant Lib as Storage Lib
    participant Service as Storage Service
    participant FS as File System

    Client->>Lib: storage_open_file()
    Lib->>Service: STORAGE_FILE_OPEN (TIPC)
    Service->>FS: fs_open_file()
    FS-->>Service: file_handle
    Service-->>Lib: response + handle
    Lib-->>Client: file_handle_t
```

### 3.3 事务处理机制

#### 3.3.1 事务结构定义
```c
struct transaction {
    struct fs* fs;
    struct list_node node;
    struct list_node open_files;
    struct block_set tmp_allocated;
    struct block_set allocated;
    struct block_set freed;
    struct block_tree files_added;
    struct block_tree files_updated;
    struct block_tree files_removed;
    bool failed;
    bool invalid_block_found;
    bool complete;
    bool rebuild_free_set;
    bool repaired;
    data_block_t min_free_block;
    data_block_t last_free_block;
    data_block_t last_tmp_free_block;
    struct block_set* new_free_set;
};
```

#### 3.3.2 事务生命周期
1. **事务激活**：`transaction_activate()` - 初始化事务状态
2. **操作执行**：文件操作在事务上下文中执行
3. **事务提交**：`transaction_complete()` - 原子性提交所有更改
4. **错误回滚**：失败时自动回滚到事务开始状态

## 4. 块设备和存储后端

### 4.1 块设备抽象层

#### 4.1.1 块设备类型
- **RPMB 设备**：`block_device_tipc.c` 中的 RPMB 存储实现
- **NS 设备**：非安全存储设备的 TIPC 代理实现
- **内存设备**：测试和模拟用途的内存块设备

#### 4.1.2 块设备操作接口
```c
struct block_device_ops {
    void (*start_read)(struct block_device* dev, data_block_t block);
    void (*start_write)(struct block_device* dev, data_block_t block,
                       const void* data, size_t data_size, bool sync);
    // ... 其他操作接口
};
```

### 4.2 加密和安全机制

#### 4.2.1 密钥管理
```c
struct key {
    uint8_t byte[32];  // 256位密钥
};
```

#### 4.2.2 数据完整性保护
- **MAC 验证**：每个块都有对应的 MAC 值进行完整性验证
- **加密存储**：所有数据使用 AES 加密存储
- **防篡改机制**：通过 RPMB 或其他安全存储提供防篡改保护

## 5. 架构特点总结

### 5.1 设计优势
1. **模块化架构**：清晰的模块分离和职责划分
2. **事务安全**：完整的事务处理机制保证数据一致性
3. **多级安全**：支持不同安全级别的存储需求
4. **TIPC 通信**：高效的进程间通信机制
5. **块设备抽象**：灵活的存储后端支持

### 5.2 技术约束
1. **用户空间限制**：所有操作在用户空间执行
2. **TIPC 依赖**：依赖 Trusty 特有的 TIPC 通信机制
3. **块设备模型**：基于固定大小块的存储模型
4. **单一命名空间**：文件系统采用平坦的命名空间

### 5.3 与 GP 标准的兼容性分析
1. **接口差异**：现有接口与 GP 标准存储 API 存在显著差异
2. **对象模型缺失**：缺少 GP 标准的对象句柄和属性管理机制
3. **存储类型支持**：需要扩展以支持 GP 标准的存储类型分类
4. **枚举功能不足**：现有目录枚举功能需要增强以支持 GP 对象枚举

---

**分析完成状态**：✅ 任务一第一部分完成  
**下一步**：继续深入分析存储服务的具体实现细节和数据流程
