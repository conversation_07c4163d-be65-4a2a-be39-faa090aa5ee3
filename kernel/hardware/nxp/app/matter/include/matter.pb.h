/*
 * SPDX-License-Identifier: Apache-2.0
 *
 * Copyright 2023 NXP
 *
 */

/* Automatically generated nanopb header */
/* Generated by nanopb-0.3.9.8 at Mon Dec  5 15:50:45 2022. */

#ifndef PB_MATTER_PB_H_INCLUDED
#define PB_MATTER_PB_H_INCLUDED
#include <pb.h>

/* @@protoc_insertion_point(includes) */
#if PB_PROTO_HEADER_VERSION != 30
#error Regenerate this file with the current version of nanopb generator.
#endif

#ifdef __cplusplus
extern "C" {
#endif

/* Struct definitions */
typedef PB_BYTES_ARRAY_T(2048) MatterCert_cert_t;
typedef struct _MatterCert {
    bool has_cert;
    MatterCert_cert_t cert;
/* @@protoc_insertion_point(struct:MatterCert) */
} MatterCert;

/* Default values for struct fields */

/* Initializer values for message structs */
#define MatterCert_init_default                  {false, {0, {0}}}
#define MatterCert_init_zero                     {false, {0, {0}}}

/* Field tags (for use in manual encoding/decoding) */
#define MatterCert_cert_tag                      1

/* Struct field encoding specification for nanopb */
extern const pb_field_t MatterCert_fields[2];

/* Maximum encoded size of messages (where known) */
#define MatterCert_size                          2051

/* Message IDs (where set with "msgid" option) */
#ifdef PB_MSGID

#define MATTER_MESSAGES \


#endif

#ifdef __cplusplus
} /* extern "C" */
#endif
/* @@protoc_insertion_point(eof) */

#endif
