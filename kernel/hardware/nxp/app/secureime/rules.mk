# Copyright (C) 2020 The Android Open Source Project
#
# Copyright 2022 NXP
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

LOCAL_DIR := $(GET_LOCAL_DIR)

LIBTEEUI_ROOT := $(TRUSTY_TOP)/system/teeui/libteeui
FREETYPE_ROOT := $(TRUSTY_TOP)/external/freetype

MODULE := $(LOCAL_DIR)

MANIFEST := \
        $(LOCAL_DIR)/manifest.json

MODULE_INCLUDES += \
        $(LOCAL_DIR) \
        $(LOCAL_DIR)/layouts/include \
        $(LIBTEEUI_ROOT)/include \
        trusty/user/base/lib/tipc \

MODULE_SRCS += \
        $(LOCAL_DIR)/main.cpp \
        $(LOCAL_DIR)/secureime.cpp \
        $(LOCAL_DIR)/keyboard_view.cpp \
        $(LIBTEEUI_ROOT)/src/label.cpp \
        $(LIBTEEUI_ROOT)/src/utils.cpp \
        $(LIBTEEUI_ROOT)/src/font_rendering.cpp \
        $(LIBTEEUI_ROOT)/src/button.cpp \

MODULE_LIBRARY_DEPS += \
        $(LOCAL_DIR)/layouts \
        trusty/user/base/lib/libc-rctee \
        trusty/user/base/lib/libstdc++-trusty \
        trusty/user/base/lib/tipc \
        trusty/user/base/lib/freetype-stub \
        trusty/user/base/lib/rng \
        $(FREETYPE_ROOT)/devel-teeui \
        trusty/hardware/nxp/base/lib/hwsecure \

include make/trusted_app.mk
