/*
 * SPDX-License-Identifier: Apache-2.0
 *
 * Copyright 2023 NXP
 */

#ifndef __ELE_H__
#define __ELE_H__

int generate_ele_rpmb_key(uint8_t *kbuf, size_t* klen);
int get_ele_huk(void);
int get_ele_derived_key(uint8_t *key, size_t key_size, uint8_t *ctx, size_t ctx_size);
/* ELE generic cipher to handle AES ECB and AES CBC. */
int ele_generic_cipher(bool encrypt, const uint8_t *iv, size_t iv_len,
                       const uint8_t *key, size_t key_len, const uint8_t *input,
                       uint8_t *output, size_t data_len, enum hwaes_mode algo);
/* ELE generic cipher to handle AES GCM. */
int ele_generic_aead(bool encrypt, const uint8_t *iv, size_t iv_len,
                     const uint8_t *key, size_t key_len, const uint8_t *aad,
                     size_t aad_len, const uint8_t *input, uint8_t *output,
                     size_t data_len, const uint8_t *tag_in, size_t tag_in_len,
                     uint8_t *tag_out, size_t tag_out_len, enum hwaes_mode algo);

#endif //__ELE_H__
