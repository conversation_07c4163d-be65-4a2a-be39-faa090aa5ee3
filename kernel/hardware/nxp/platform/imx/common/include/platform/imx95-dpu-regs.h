/*
 * Copyright 2024 NXP
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#ifndef __IMX95_DPU_REGS_H
#define __IMX95_DPU_REGS_H

#define DPU_REGS_PHY 0x4b400000
#define DPU_BASE_VIRT (0xFFFFFFFF00000000 + DPU_REGS_PHY)
#define DPU_REGS_SIZE 0x400000
#define DPU_PHY2VIRT(phy) (0xFFFFFFFF00000000 + (phy))

/* Const Frame*/
#define DPU_CONSTFRAME0  0xf0000
#define DPU_CONSTFRAME1  0x130000
#define DPU_CONSTFRAME2  0x100000
#define DPU_CONSTFRAME3  0x140000
#define DPU_AUX_CONSTFRAME0  0xf1000
#define DPU_AUX_CONSTFRAME1  0x131000
#define DPU_AUX_CONSTFRAME2  0x101000
#define DPU_AUX_CONSTFRAME3  0x141000

/* Domain Blend */
#define DPU_DOMAINBLEND0  0x2a0000
#define DPU_DOMAINBLEND1  0x320000

/* Dither */
#define DPU_DITHER0    0x310000
#define DPU_DITHER1    0x370000
#define DPU_AUX_DITHER0  0x311000
#define DPU_AUX_DITHER1  0x371020

/* ExtDst */
#define DPU_EXTDST0    0x110000
#define DPU_EXTDST1    0x150000
#define DPU_EXTDST2    0x120000
#define DPU_EXTDST3    0x160000
#define DPU_AUX_EXTDST0  0x111000
#define DPU_AUX_EXTDST1  0x151000
#define DPU_AUX_EXTDST2  0x121000
#define DPU_AUX_EXTDST3  0x161000

/* FetchEco */
#define DPU_FETCHECO0  0x210000
#define DPU_FETCHECO1  0x230000
#define DPU_FETCHECO2  0x250000
#define DPU_FETCHECO3  0xa0000
#define DPU_AUX_FETCHECO0 0x211000
#define DPU_AUX_FETCHECO1 0x231000
#define DPU_AUX_FETCHECO2 0x251000
#define DPU_AUX_FETCHECO3 0xa1000

/* FrameGen */
#define DPU_FRAMEGEN0  0x2b0000
#define DPU_FRAMEGEN1  0x330000

/* Fetch YUV */
#define DPU_FETCHYUV0  0x200000
#define DPU_FETCHYUV1  0x220000
#define DPU_FETCHYUV2  0x240000
#define DPU_FETCHYUV3  0x1f0000
#define DPU_AUX_FETCHYUV0 0x201000
#define DPU_AUX_FETCHYUV1 0x221000
#define DPU_AUX_FETCHYUV2 0x241000
#define DPU_AUX_FETCHYUV3 0x1f1000

/* Hs scaler */
#define DPU_HSCALER0   0x270000
#define DPU_HSCALER1   0xb0000
#define DPU_AUX_HSCALER0 0x271000
#define DPU_AUX_HSCALER1 0xb1000

/* Fetch Layer */
#define DPU_FETCHLAYER0  0x1d0000
#define DPU_FETCHLAYER1  0x1e0000
#define DPU_AUX_FETCHLAYER0 0x1d1000
#define DPU_AUX_FETCHLAYER1 0x1e1000

/* protected regs*/
#define DPU_FRAMEBUFFEMB (0x04 + 0x18)
#define DPU_FRAMEBUFFE (0x00 + 0x18)

/* LayerBlend */
#define DPU_LAYERBLEND0   0x170000
#define DPU_LAYERBLEND1   0x180000
#define DPU_LAYERBLEND2   0x190000
#define DPU_LAYERBLEND3   0x1a0000
#define DPU_LAYERBLEND4   0x1b0000
#define DPU_LAYERBLEND5   0x1c0000

#define DPU_AUX_LAYERBLEND0   0x171000
#define DPU_AUX_LAYERBLEND1   0x181000
#define DPU_AUX_LAYERBLEND2   0x191000
#define DPU_AUX_LAYERBLEND3   0x1a1000
#define DPU_AUX_LAYERBLEND4   0x1b1000
#define DPU_AUX_LAYERBLEND5   0x1c1000

/* Blit */
#define DPU_BLIT 0x0

#endif
