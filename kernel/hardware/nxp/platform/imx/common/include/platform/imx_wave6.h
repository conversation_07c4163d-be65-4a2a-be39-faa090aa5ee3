/*
 * Copyright 2024 NXP
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#ifndef __IMX_WAVE6_H
#define __IMX_WAVE6_H


#define SYSCALL_PLATFORM_FD_WAVE6                      0x7
#define WAVE6_SECURE_MODE                              0x00000001

struct wave6_secure_mode_msg {
    uint32_t secure_mode;
};

#endif
