/*
 * Copyright 2023 NXP
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#ifndef _IMX_AMPHION_H_
#define _IMX_AMPHION_H_

struct vpu_fastcall_message {
        uint32_t secure_memory_offset;
        uint32_t src_offset;
        uint32_t dst_offset;
        uint64_t size;
};

struct vpu_ctx {
        void*    message_buffer;
        uint64_t message_buffer_id;
        uint32_t message_buffer_size;
        uint64_t message_client_id;

        void*    hdr_buffer;
        uint64_t hdr_buffer_id;
        uint32_t hdr_buffer_size;
        uint64_t hdr_client_id;
};

#define SYSCALL_PLATFORM_FD_AMPHION 0x8
#define AMPHION_CLEAR_BOOT_BUFFER 0x00000001
#define AMPHION_GET_FIRMWARE_POWER 0x00000002

#endif

