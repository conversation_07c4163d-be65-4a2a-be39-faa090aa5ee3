/*
 * Copyright 2024 NXP
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#ifndef __IMX_WAVE6_REGS_H
#define __IMX_WAVE6_REGS_H

#define VPU_REGS_PHY 0x4c490000
#define VPU_BASE_VIRT (0xFFFFFFFF00000000 + VPU_REGS_PHY)
#define VPU_REGS_SIZE 0x10000

#define VCPU_REGS_PHY 0x4c4c0000
#define VCPU_BASE_VIRT (0xFFFFFFFF00000000 + VCPU_REGS_PHY)
#define VCPU_REGS_SIZE 0x10000

#define W6_REG_BASE     0x00000000
#define W6_CMD_REG_BASE 0x00000200
#define W6_CMD_REG_END  0x00000600

//#define W6_CMD_DEC_INIT_SEQ_BS_RD_PTR                 (W6_REG_BASE + 0x300)
//#define W6_CMD_DEC_INIT_SEQ_BS_WR_PTR                 (W6_REG_BASE + 0x304)
#define W6_CMD_DEC_PIC_BS_RD_PTR                      (W6_REG_BASE + 0x300)
#define W6_CMD_DEC_PIC_BS_WR_PTR                      (W6_REG_BASE + 0x304)

#define W6_CMD_DEC_SET_DISP_Y_BASE                    (W6_REG_BASE + 0x30C)
#define W6_CMD_DEC_SET_DISP_CB_BASE                   (W6_REG_BASE + 0x310)
#define W6_CMD_DEC_SET_DISP_CR_BASE                   (W6_REG_BASE + 0x314)

//#define W6_CMD_DEC_SET_FB_FBC_Y0                      (W6_REG_BASE + 0x310)
//#define W6_CMD_DEC_SET_FB_FBC_C0                      (W6_REG_BASE + 0x314)
#define W6_CMD_DEC_SET_FB_FBC_Y_OFFSET0               (W6_REG_BASE + 0x318)
#define W6_CMD_DEC_SET_FB_FBC_C_OFFSET0               (W6_REG_BASE + 0x31C)
#define W6_CMD_DEC_SET_FB_MV_COL0                     (W6_REG_BASE + 0x320)
#define W6_CMD_DEC_SET_FB_SUB_SAMPLED0                (W6_REG_BASE + 0x324)
#define W6_CMD_DEC_SET_FB_FBC_Y1                      (W6_REG_BASE + 0x328)
#define W6_CMD_DEC_SET_FB_FBC_C1                      (W6_REG_BASE + 0x32C)
#define W6_CMD_DEC_SET_FB_FBC_Y_OFFSET1               (W6_REG_BASE + 0x330)
#define W6_CMD_DEC_SET_FB_FBC_C_OFFSET1               (W6_REG_BASE + 0x334)
#define W6_CMD_DEC_SET_FB_MV_COL1                     (W6_REG_BASE + 0x338)
#define W6_CMD_DEC_SET_FB_SUB_SAMPLED1                (W6_REG_BASE + 0x33C)
#define W6_CMD_DEC_SET_FB_FBC_Y2                      (W6_REG_BASE + 0x340)
#define W6_CMD_DEC_SET_FB_FBC_C2                      (W6_REG_BASE + 0x344)
#define W6_CMD_DEC_SET_FB_FBC_Y_OFFSET2               (W6_REG_BASE + 0x348)
#define W6_CMD_DEC_SET_FB_FBC_C_OFFSET2               (W6_REG_BASE + 0x34C)
#define W6_CMD_DEC_SET_FB_MV_COL2                     (W6_REG_BASE + 0x350)
#define W6_CMD_DEC_SET_FB_SUB_SAMPLED2                (W6_REG_BASE + 0x354)
#define W6_CMD_DEC_SET_FB_FBC_Y3                      (W6_REG_BASE + 0x358)
#define W6_CMD_DEC_SET_FB_FBC_C3                      (W6_REG_BASE + 0x35C)
#define W6_CMD_DEC_SET_FB_FBC_Y_OFFSET3               (W6_REG_BASE + 0x360)
#define W6_CMD_DEC_SET_FB_FBC_C_OFFSET3               (W6_REG_BASE + 0x364)
#define W6_CMD_DEC_SET_FB_MV_COL3                     (W6_REG_BASE + 0x368)
#define W6_CMD_DEC_SET_FB_SUB_SAMPLED3                (W6_REG_BASE + 0x36C)
#define W6_CMD_DEC_SET_FB_FBC_Y4                      (W6_REG_BASE + 0x370)
#define W6_CMD_DEC_SET_FB_FBC_C4                      (W6_REG_BASE + 0x374)
#define W6_CMD_DEC_SET_FB_FBC_Y_OFFSET4               (W6_REG_BASE + 0x378)
#define W6_CMD_DEC_SET_FB_FBC_C_OFFSET4               (W6_REG_BASE + 0x37C)
#define W6_CMD_DEC_SET_FB_MV_COL4                     (W6_REG_BASE + 0x380)
#define W6_CMD_DEC_SET_FB_SUB_SAMPLED4                (W6_REG_BASE + 0x384)
#define W6_CMD_DEC_SET_FB_FBC_Y5                      (W6_REG_BASE + 0x388)
#define W6_CMD_DEC_SET_FB_FBC_C5                      (W6_REG_BASE + 0x38C)
#define W6_CMD_DEC_SET_FB_FBC_Y_OFFSET5               (W6_REG_BASE + 0x390)
#define W6_CMD_DEC_SET_FB_FBC_C_OFFSET5               (W6_REG_BASE + 0x394)
#define W6_CMD_DEC_SET_FB_MV_COL5                     (W6_REG_BASE + 0x398)
#define W6_CMD_DEC_SET_FB_SUB_SAMPLED5                (W6_REG_BASE + 0x39C)
#define W6_CMD_DEC_SET_FB_FBC_Y6                      (W6_REG_BASE + 0x3A0)
#define W6_CMD_DEC_SET_FB_FBC_C6                      (W6_REG_BASE + 0x3A4)
#define W6_CMD_DEC_SET_FB_FBC_Y_OFFSET6               (W6_REG_BASE + 0x3A8)
#define W6_CMD_DEC_SET_FB_FBC_C_OFFSET6               (W6_REG_BASE + 0x3AC)
#define W6_CMD_DEC_SET_FB_MV_COL6                     (W6_REG_BASE + 0x3B0)
#define W6_CMD_DEC_SET_FB_SUB_SAMPLED6                (W6_REG_BASE + 0x3B4)
#define W6_CMD_DEC_SET_FB_FBC_Y7                      (W6_REG_BASE + 0x3B8)
#define W6_CMD_DEC_SET_FB_FBC_C7                      (W6_REG_BASE + 0x3BC)
#define W6_CMD_DEC_SET_FB_FBC_Y_OFFSET7               (W6_REG_BASE + 0x3C0)
#define W6_CMD_DEC_SET_FB_FBC_C_OFFSET7               (W6_REG_BASE + 0x3C4)
#define W6_CMD_DEC_SET_FB_MV_COL7                     (W6_REG_BASE + 0x3C8)
#define W6_CMD_DEC_SET_FB_SUB_SAMPLED7                (W6_REG_BASE + 0x3CC)
#define W6_CMD_DEC_SET_FB_FBC_Y8                      (W6_REG_BASE + 0x3D0)
#define W6_CMD_DEC_SET_FB_FBC_C8                      (W6_REG_BASE + 0x3D4)
#define W6_CMD_DEC_SET_FB_FBC_Y_OFFSET8               (W6_REG_BASE + 0x3D8)
#define W6_CMD_DEC_SET_FB_FBC_C_OFFSET8               (W6_REG_BASE + 0x3DC)
#define W6_CMD_DEC_SET_FB_MV_COL8                     (W6_REG_BASE + 0x3E0)
#define W6_CMD_DEC_SET_FB_SUB_SAMPLED8                (W6_REG_BASE + 0x3E4)
#define W6_CMD_DEC_SET_FB_FBC_Y9                      (W6_REG_BASE + 0x3E8)
#define W6_CMD_DEC_SET_FB_FBC_C9                      (W6_REG_BASE + 0x3EC)
#define W6_CMD_DEC_SET_FB_FBC_Y_OFFSET9               (W6_REG_BASE + 0x3F0)
#define W6_CMD_DEC_SET_FB_FBC_C_OFFSET9               (W6_REG_BASE + 0x3F4)
#define W6_CMD_DEC_SET_FB_MV_COL9                     (W6_REG_BASE + 0x3F8)
#define W6_CMD_DEC_SET_FB_SUB_SAMPLED9                (W6_REG_BASE + 0x3FC)
#define W6_CMD_DEC_SET_FB_FBC_Y10                     (W6_REG_BASE + 0x400)
#define W6_CMD_DEC_SET_FB_FBC_C10                     (W6_REG_BASE + 0x404)
#define W6_CMD_DEC_SET_FB_FBC_Y_OFFSET10              (W6_REG_BASE + 0x408)
#define W6_CMD_DEC_SET_FB_FBC_C_OFFSET10              (W6_REG_BASE + 0x40C)
#define W6_CMD_DEC_SET_FB_MV_COL10                    (W6_REG_BASE + 0x410)
#define W6_CMD_DEC_SET_FB_SUB_SAMPLED10               (W6_REG_BASE + 0x414)
#define W6_CMD_DEC_SET_FB_FBC_Y11                     (W6_REG_BASE + 0x418)
#define W6_CMD_DEC_SET_FB_FBC_C11                     (W6_REG_BASE + 0x41C)
#define W6_CMD_DEC_SET_FB_FBC_Y_OFFSET11              (W6_REG_BASE + 0x420)
#define W6_CMD_DEC_SET_FB_FBC_C_OFFSET11              (W6_REG_BASE + 0x424)
#define W6_CMD_DEC_SET_FB_MV_COL11                    (W6_REG_BASE + 0x428)
#define W6_CMD_DEC_SET_FB_SUB_SAMPLED11               (W6_REG_BASE + 0x42C)
#define W6_CMD_DEC_SET_FB_FBC_Y12                     (W6_REG_BASE + 0x430)
#define W6_CMD_DEC_SET_FB_FBC_C12                     (W6_REG_BASE + 0x434)
#define W6_CMD_DEC_SET_FB_FBC_Y_OFFSET12              (W6_REG_BASE + 0x438)
#define W6_CMD_DEC_SET_FB_FBC_C_OFFSET12              (W6_REG_BASE + 0x43C)
#define W6_CMD_DEC_SET_FB_MV_COL12                    (W6_REG_BASE + 0x440)
#define W6_CMD_DEC_SET_FB_SUB_SAMPLED12               (W6_REG_BASE + 0x444)
#define W6_CMD_DEC_SET_FB_FBC_Y13                     (W6_REG_BASE + 0x448)
#define W6_CMD_DEC_SET_FB_FBC_C13                     (W6_REG_BASE + 0x44C)
#define W6_CMD_DEC_SET_FB_FBC_Y_OFFSET13              (W6_REG_BASE + 0x450)
#define W6_CMD_DEC_SET_FB_FBC_C_OFFSET13              (W6_REG_BASE + 0x454)
#define W6_CMD_DEC_SET_FB_MV_COL13                    (W6_REG_BASE + 0x458)
#define W6_CMD_DEC_SET_FB_SUB_SAMPLED13               (W6_REG_BASE + 0x45C)
#define W6_CMD_DEC_SET_FB_FBC_Y14                     (W6_REG_BASE + 0x460)
#define W6_CMD_DEC_SET_FB_FBC_C14                     (W6_REG_BASE + 0x464)
#define W6_CMD_DEC_SET_FB_FBC_Y_OFFSET14              (W6_REG_BASE + 0x468)
#define W6_CMD_DEC_SET_FB_FBC_C_OFFSET14              (W6_REG_BASE + 0x46C)
#define W6_CMD_DEC_SET_FB_MV_COL14                    (W6_REG_BASE + 0x470)
#define W6_CMD_DEC_SET_FB_SUB_SAMPLED14               (W6_REG_BASE + 0x474)
#define W6_CMD_DEC_SET_FB_FBC_Y15                     (W6_REG_BASE + 0x478)
#define W6_CMD_DEC_SET_FB_FBC_C15                     (W6_REG_BASE + 0x47C)
#define W6_CMD_DEC_SET_FB_FBC_Y_OFFSET15              (W6_REG_BASE + 0x480)
#define W6_CMD_DEC_SET_FB_FBC_C_OFFSET15              (W6_REG_BASE + 0x484)
#define W6_CMD_DEC_SET_FB_MV_COL15                    (W6_REG_BASE + 0x488)
#define W6_CMD_DEC_SET_FB_SUB_SAMPLED15               (W6_REG_BASE + 0x48C)
#define W6_CMD_DEC_SET_FB_DEFAULT_CDF                 (W6_REG_BASE + 0x494)
#define W6_CMD_DEC_SET_FB_SEGMAP                      (W6_REG_BASE + 0x498)
#define W6_CMD_DEC_SET_FB_MV_COL_PRE_ENT              (W6_REG_BASE + 0x4DC)
#define W6_CMD_DEC_SET_FB_FBC_CR0                     (W6_REG_BASE + 0x4F0)
#define W6_CMD_DEC_SET_FB_FBC_CR_OFFSET0              (W6_REG_BASE + 0x4F4)
#define W6_CMD_DEC_SET_FB_FBC_CR1                     (W6_REG_BASE + 0x4F8)
#define W6_CMD_DEC_SET_FB_FBC_CR_OFFSET1              (W6_REG_BASE + 0x4FC)
#define W6_CMD_DEC_SET_FB_FBC_CR2                     (W6_REG_BASE + 0x500)
#define W6_CMD_DEC_SET_FB_FBC_CR_OFFSET2              (W6_REG_BASE + 0x504)
#define W6_CMD_DEC_SET_FB_FBC_CR3                     (W6_REG_BASE + 0x508)
#define W6_CMD_DEC_SET_FB_FBC_CR_OFFSET3              (W6_REG_BASE + 0x50C)
#define W6_CMD_DEC_SET_FB_FBC_CR4                     (W6_REG_BASE + 0x510)
#define W6_CMD_DEC_SET_FB_FBC_CR_OFFSET4              (W6_REG_BASE + 0x514)
#define W6_CMD_DEC_SET_FB_FBC_CR5                     (W6_REG_BASE + 0x518)
#define W6_CMD_DEC_SET_FB_FBC_CR_OFFSET5              (W6_REG_BASE + 0x51C)
#define W6_CMD_DEC_SET_FB_FBC_CR6                     (W6_REG_BASE + 0x520)
#define W6_CMD_DEC_SET_FB_FBC_CR_OFFSET6              (W6_REG_BASE + 0x524)
#define W6_CMD_DEC_SET_FB_FBC_CR7                     (W6_REG_BASE + 0x528)
#define W6_CMD_DEC_SET_FB_FBC_CR_OFFSET7              (W6_REG_BASE + 0x52C)
#define W6_CMD_DEC_SET_FB_FBC_CR8                     (W6_REG_BASE + 0x530)
#define W6_CMD_DEC_SET_FB_FBC_CR_OFFSET8              (W6_REG_BASE + 0x534)
#define W6_CMD_DEC_SET_FB_FBC_CR9                     (W6_REG_BASE + 0x538)
#define W6_CMD_DEC_SET_FB_FBC_CR_OFFSET9              (W6_REG_BASE + 0x53C)
#define W6_CMD_DEC_SET_FB_FBC_CR10                    (W6_REG_BASE + 0x540)
#define W6_CMD_DEC_SET_FB_FBC_CR_OFFSET10             (W6_REG_BASE + 0x544)
#define W6_CMD_DEC_SET_FB_FBC_CR11                    (W6_REG_BASE + 0x548)
#define W6_CMD_DEC_SET_FB_FBC_CR_OFFSET11             (W6_REG_BASE + 0x54C)
#define W6_CMD_DEC_SET_FB_FBC_CR12                    (W6_REG_BASE + 0x550)
#define W6_CMD_DEC_SET_FB_FBC_CR_OFFSET12             (W6_REG_BASE + 0x554)
#define W6_CMD_DEC_SET_FB_FBC_CR13                    (W6_REG_BASE + 0x558)
#define W6_CMD_DEC_SET_FB_FBC_CR_OFFSET13             (W6_REG_BASE + 0x55C)
#define W6_CMD_DEC_SET_FB_FBC_CR14                    (W6_REG_BASE + 0x560)
#define W6_CMD_DEC_SET_FB_FBC_CR_OFFSET14             (W6_REG_BASE + 0x564)
#define W6_CMD_DEC_SET_FB_FBC_CR15                    (W6_REG_BASE + 0x568)
#define W6_CMD_DEC_SET_FB_FBC_CR_OFFSET15             (W6_REG_BASE + 0x56C)

#define W6_DEC_PIC                                     0x0100
#define W6_INIT_SEQ                                    0x0040
#define W6_DEC_SET_DISP_BUF                            0x0400
#define W6_SET_FB                                      0x0080
#define W6_COMMAND                                    (W6_REG_BASE + 0x200)

#define W6_VPU_REMAP_PADDR_GB                         (W6_REG_BASE + 0x0068)

#endif
