/*
 * Copyright 2023 NXP
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#ifndef _CSU_COMMON_H_
#define _CSU_COMMON_H_

#define SYSCALL_PLATFORM_FD_CSU 0x8 //Maxium 0xA

#define CSU_OK 0
#define CSU_ERR 1

#define CSU_IOCMD_STATUS        0x00000001
#define CSU_IOCMD_CFG_SA        0x00000002
#define CSU_IOCMD_CFG_CSL       0x00000003
#define CSU_IOCMD_SECURE_DISP   0x00000004

struct csu_cfg_sa_msg {
    uint32_t id;
    uint32_t enable;
};

struct csu_cfg_csl_msg {
    uint32_t id;
    uint32_t val;
};

struct csu_cfg_secure_disp_msg {
    uint32_t enable;
    uint32_t paddr;
};

#endif
