/*
 * Copyright 2023 NXP
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#ifndef __IMX_ELE_H_
#define __IMX_ELE_H_

#include <stdbool.h>

#define SYSCALL_PLATFORM_FD_ELE 0x8

struct ele_huk_msg {
	uint8_t *hwkey;
	uint8_t *ctx;
	size_t key_size;
	size_t ctx_size;
};

/**
* enum ele_aes_algo - algorithm types for aes, keep syncing
* with the "enum hwaes_mode".
* @ELE_ECB_MODE:      ECB mode.
* @ELE_CBC_MODE:      CBC mode.
* @ELE_CBC_CTS_MODE:  CBC mode with ciphertext stealing (CTS).
* @ELE_CTR_MODE:      CTR mode.
* @ELE_GCM_MODE:      GCM mode.
*/
enum ele_aes_algo {
    ELE_ECB_MODE = 0,
    ELE_CBC_MODE = 1,
    ELE_CBC_CTS_MODE = 2,
    ELE_CTR_MODE = 3,
    ELE_GCM_MODE = 4,
};

struct ele_generic_cipher_msg {
	const uint8_t *input;
	const uint8_t *output;
	size_t data_len;
	const uint8_t *key;
	size_t key_len;
	const uint8_t *iv;
	size_t iv_len;
	enum ele_aes_algo algo;
	bool encrypt;
};

struct ele_generic_aead_msg {
	const uint8_t *input;
	const uint8_t *output;
	size_t data_len;
	const uint8_t *key;
	size_t key_len;
	const uint8_t *iv;
	size_t iv_len;
	const uint8_t *aad;
	size_t aad_len;
	const uint8_t *tag_in;
	size_t tag_in_len;
	uint8_t *tag_out;
	size_t tag_out_len;
	enum ele_aes_algo algo;
	bool encrypt;
};

#define ELE_DERIVE_HUK (0x00000001)
#define ELE_GENERIC_CIPHER (0x00000002)
#define ELE_GENERIC_AEAD (0x00000003)

#endif // __IMX_ELE_H_
