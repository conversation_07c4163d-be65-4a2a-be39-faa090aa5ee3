/*
 * Copyright 2024 NXP
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#ifndef __IMX_CAAM_H_
#define __IMX_CAAM_H_

#include <stdbool.h>

#define SYSCALL_PLATFORM_FD_CAAM 0x7

#define CAAM_DERIVE_PR_RNG 0x00000001
#define CAAM_GEN_BLOB 0x00000002
#define CAAM_DECAP_BLOB 0x00000003
#define CAAM_GEN_DEK_BLOB 0x00000004
#define CAAM_DECAP_DEK_BLOB 0x00000005
#define CAAM_DERIVE_KDFV1_ROOT_KEY 0x00000006
#define CAAM_AES_OP 0x00000007
#define CAAM_HASH 0x00000008
#define CAAM_AES_CBC 0x00000009
#define CAAM_AES_ECB 0x0000000a
#define CAAM_AES_CTR 0x0000000b
#define CAAM_AES_GCM 0x0000000c
#define CAAM_TDES_ECB 0x0000000d
#define CAAM_TDES_CBC 0x0000000e
#define CAAM_GEN_BKEK_KEY 0x0000000f
#define CAAM_GEN_MPPUBK 0x00000010
#define CAAM_GET_KEYBOX 0x00000011

#define CIPHER_ENCRYPT     0x1
#define CIPHER_DECRYPT     0UL

#define ALGORITHM_OPERATION_CMD_AAI_SHIFT       4

#define ALGORITHM_OPERATION_CMD_AAI_CBC         (0x10UL << ALGORITHM_OPERATION_CMD_AAI_SHIFT)
#define ALGORITHM_OPERATION_CMD_AAI_ECB         (0x20UL << ALGORITHM_OPERATION_CMD_AAI_SHIFT)
#define ALGORITHM_OPERATION_CMD_AAI_CFB         (0x30UL << ALGORITHM_OPERATION_CMD_AAI_SHIFT)
#define ALGORITHM_OPERATION_CMD_AAI_OFB         (0x40UL << ALGORITHM_OPERATION_CMD_AAI_SHIFT)
#define ALGORITHM_OPERATION_CMD_AAI_XTS         (0x50UL << ALGORITHM_OPERATION_CMD_AAI_SHIFT)
#define ALGORITHM_OPERATION_CMD_AAI_CMAC        (0x60UL << ALGORITHM_OPERATION_CMD_AAI_SHIFT)
#define ALGORITHM_OPERATION_CMD_AAI_XCBC_MAC    (0x70UL << ALGORITHM_OPERATION_CMD_AAI_SHIFT)
#define ALGORITHM_OPERATION_CMD_AAI_CCM         (0x80UL << ALGORITHM_OPERATION_CMD_AAI_SHIFT)
#define ALGORITHM_OPERATION_CMD_AAI_GCM         (0x90UL << ALGORITHM_OPERATION_CMD_AAI_SHIFT)

#define ALGORITHM_OPERATION_ALGSEL_SHIFT        16
#define ALGORITHM_OPERATION_ALGSEL_AES          (0x10UL << ALGORITHM_OPERATION_ALGSEL_SHIFT)
#define ALGORITHM_OPERATION_ALGSEL_DES          (0x20UL << ALGORITHM_OPERATION_ALGSEL_SHIFT)
#define ALGORITHM_OPERATION_ALGSEL_3DES         (0x21UL << ALGORITHM_OPERATION_ALGSEL_SHIFT)
#define ALGORITHM_OPERATION_ALGSEL_ARC4         (0x30UL << ALGORITHM_OPERATION_ALGSEL_SHIFT)
#define ALGORITHM_OPERATION_ALGSEL_MD5          (0x40UL << ALGORITHM_OPERATION_ALGSEL_SHIFT)
#define ALGORITHM_OPERATION_ALGSEL_SHA1         (0x41UL << ALGORITHM_OPERATION_ALGSEL_SHIFT)
#define ALGORITHM_OPERATION_ALGSEL_SHA224       (0x42UL << ALGORITHM_OPERATION_ALGSEL_SHIFT)
#define ALGORITHM_OPERATION_ALGSEL_SHA256       (0x43UL << ALGORITHM_OPERATION_ALGSEL_SHIFT)
#define ALGORITHM_OPERATION_ALGSEL_SHA384       (0x44UL << ALGORITHM_OPERATION_ALGSEL_SHIFT)
#define ALGORITHM_OPERATION_ALGSEL_SHA512       (0x45UL << ALGORITHM_OPERATION_ALGSEL_SHIFT)

#define CAAM_IV_SIZE	0x10

/* Dek blob */
#define CAAM_KB_HEADER_LEN 48
#define HAB_DEK_BLOB_HEADER_LEN 8
#define CAAM_SUCCESS 0
#define CAAM_FAILURE 1

/* Hash */
#define SHA1_DIGEST_LEN 20
#define SHA256_DIGEST_LEN 32

static uint8_t* sram_base;

/* 4kbyte pages */
#define CAAM_SEC_RAM_START_ADDR (sram_base)

#define SEC_MEM_PAGE0 CAAM_SEC_RAM_START_ADDR
#define SEC_MEM_PAGE1 (CAAM_SEC_RAM_START_ADDR + 0x1000)
#define SEC_MEM_PAGE2 (CAAM_SEC_RAM_START_ADDR + 0x2000)
#define SEC_MEM_PAGE3 (CAAM_SEC_RAM_START_ADDR + 0x3000)

/* Partition Owners */
#define SMPO_PART(prtn)              ((prtn) * 2)
#define SMPO_OWNER(val, prtn)        (((val) >> SMPO_PART(prtn)) & 0x3)
#define SMPO_PO_AVAIL                0x0
#define SMPO_PO_OWNED                0x3

/* Access Permission */
#define SMAPR(prtn)                  (CAAM_P0SMAPR + (prtn) * 16)
#define SMAPR_GRP1(perm)             ((perm) & 0xF)
#define SMAPR_GRP2(perm)             (((perm) & 0xF) << 4)
#define SMAPR_CSP                    (0x1 << 15)
#define SMAPR_SMAP_LCK               (0x1 << 13)
#define SMAPR_SMAG_LCK               (0x1 << 12)

/* Access Group */
#define SMAG2(prtn)                  (CAAM_P0SMAG2 + (prtn) * 16)
#define SMAG1(prtn)                  (CAAM_P0SMAG1 + (prtn) * 16)

/* Command */
#define SMCR_PAGE(page)              (((page) & UINT16_MAX) << 16)
#define SMCR_PRTN(prtn)              (((prtn) & 0xF) << 8)
#define SMCR_CMD(cmd)                (((cmd) & 0xF) << 0)
#define SMCR_PAGE_ALLOC              0x1
#define SMCR_PAGE_DEALLOC            0x2
#define SMCR_PARTITION_DEALLOC       0x3
#define SMCR_PAGE_INQ                0x5

/* Command Status */
#define SMCSR_CERR(val)              (((val) >> 14) & 0x3)
#define SMCSR_CERR_NO_ERROR          0x0
#define SMCSR_CERR_NOT_COMPLETED     0x1
#define SMCSR_AERR(val)              (((val) >> 12) & 0x3)
#define SMCSR_AERR_NO_ERROR          0x0
#define SMCSR_PO(val)                (((val) >> 6) & 0x3)
#define SMCSR_PO_AVAILABLE           0x0
#define SMCSR_PO_UNKNOWN             0x1
#define SMCSR_PO_OWNED_BY_OTHER      0x2
#define SMCSR_PO_OWNED               0x3
#define SMCSR_PRTN(val)              ((val) & 0x3)

/*
 * Secure Memory Access Permission allowed
 */
#define GRP_READ     (0x1) /* Read allowed */
#define GRP_WRITE    (0x1 << 1) /* Write allowed */
#define GRP_TDO      (0x1 << 2) /* Trusted Descriptor override allowed */
#define GRP_BLOB     (0x1 << 3) /* Export/Import Secure Memory blobs allowed */

/*
 * HAB Blob header values
 */
#define HAB_HDR_TAG             0x81
#define HAB_HDR_V4              0x43
#define HAB_HDR_MODE_CCM        0x66
#define HAB_HDR_ALG_AES         0x55

/*
 * Generate MPPUBK
 */
#define FSL_CAAM_MP_PUBK_BYTES 64
#define PDB_MP_CSEL_SHIFT 17
#define PDB_MP_CSEL_WIDTH 4
#define PDB_MP_CSEL_P256 0x3UL << PDB_MP_CSEL_SHIFT /* P-256 */
#define PDB_MP_CSEL_P384 0x4UL << PDB_MP_CSEL_SHIFT /* P-384 */
#define PDB_MP_CSEL_P521 0x5UL << PDB_MP_CSEL_SHIFT /* P-521 */


struct kdfv1_msg {
    uint8_t* out;
    uint32_t size;
};

struct gen_blob_msg {
    uint32_t kmod_paddr;
    size_t kmod_size;
    uint32_t plain_paddr;
    uint32_t blob_paddr;
    uint32_t size;
};

struct decap_blob_msg {
    uint32_t kmod_paddr;
    size_t kmod_size;
    uint32_t plain_paddr;
    uint32_t blob_paddr;
    uint32_t size;
};

struct gen_dek_blob_msg{
    uint8_t *dek;
    size_t dek_size;
    uint8_t* dek_blob;
};

struct decap_dek_blob_msg{
    uint8_t *dek;
    size_t dek_blob_size;
    uint8_t* dek_blob;
};

struct pr_rng_msg {
    uint8_t *buf;
    size_t len;
};

struct aes_msg {
    uint32_t key_paddr;
    size_t key_size;
    uint32_t in_paddr;
    uint32_t out_paddr;
    size_t len;
    bool enc;
};

enum hash_algo {
    SHA1 = 0,
    SHA256
};

struct caam_hash_msg {
    uint32_t in_paddr;
    uint32_t out_paddr;
    uint32_t len;
    enum hash_algo algo;
};

struct aes_cbc_ctr_msg {
    uint32_t enc_flag;
    uint32_t iv_paddr;
    uint32_t key_paddr;
    size_t key_size;
    uint32_t input_text_sg_paddr;
    uint32_t output_text_sg_paddr;
    size_t size;
};

struct aes_ecb_msg {
    uint32_t enc_flag;
    uint32_t key_paddr;
    size_t key_size;
    uint32_t input_text_sg_paddr;
    uint32_t output_text_sg_paddr;
    size_t size;
};

struct aes_gcm_msg {
    uint32_t enc_flag;
    uint32_t iv_paddr;
    size_t iv_size;
    size_t key_paddr;
    size_t key_size;
    size_t aad_sg_paddr;
    size_t aad_size;
    uint32_t input_text_sg_paddr;
    uint32_t output_text_sg_paddr;
    size_t text_size;
    uint32_t tag_paddr;
    size_t tag_size;
};
struct tdes_ecb_msg {
    uint32_t enc_flag;
    uint32_t key_paddr;
    size_t key_size;
    uint32_t input_text_sg_paddr;
    uint32_t output_text_sg_paddr;
    size_t text_size;
};

struct tdes_cbc_msg {
    uint32_t enc_flag;
    uint32_t iv_paddr;
    size_t iv_size;
    uint32_t key_paddr;
    size_t key_size;
    uint32_t input_text_sg_paddr;
    uint32_t output_text_sg_paddr;
    size_t text_size;
};

struct hab_dek_blob_header {
    uint8_t tag;            /* Constant identifying HAB struct: 0x81 */
    uint8_t len_msb;        /* Struct length in 8-bit msb */
    uint8_t len_lsb;        /* Struct length in 8-bit lsb */
    uint8_t par;            /* Constant value, HAB version: 0x43 */
    uint8_t mode;           /* AES encryption CCM mode: 0x66 */
    uint8_t alg;            /* AES encryption alg: 0x55 */
    uint8_t size;           /* Unwrapped key value size in bytes */
    uint8_t flg;            /* Key flags */
} __attribute__((aligned(8)));

struct gen_bkek_key_msg {
    const uint8_t *kmod;
    size_t kmod_size;
    uint32_t out;
    size_t out_size;
};

struct gen_mppubk_msg {
    uint32_t out;
    size_t out_size;
};

struct keybox_msg {
    void *kbox;
    size_t kbox_size;
};

#endif // __IMX_CAAM_H_
