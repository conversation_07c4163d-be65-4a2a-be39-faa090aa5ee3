/*
 * Copyright 2024 NXP
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <lk/init.h>
#include <platform.h>
#include <arch/ops.h>
#include <imx-regs.h>
#include <kernel/vm.h>
#include <kernel/mutex.h>
#include <kernel/usercopy.h>
#include <lib/rctee/sys_fd.h>
#include <err.h>
#include <reg.h>
#include "imx_caam.h"
#include <platform/imx_caam.h>
#include <uapi/rctee_uuid.h>
#include <lib/rctee/rctee_app.h>

#define TLOG_TAG "caam_kernel"
#include <trusty_log.h>

#define DRIVER_FD SYSCALL_PLATFORM_FD_CAAM
#define CHECK_FD(x) \
        do { if(x!=DRIVER_FD) return ERR_BAD_HANDLE; } while (0)

static struct uuid hwcrypto_ta_uuid = {
    0x1adaf827,
    0x806b,
    0x4bcf,
    {0xbc, 0xec, 0x7e, 0x7d, 0x2f, 0x5a, 0x0a, 0x5c},
};

struct caam_job_rings {
    uint32_t in[1];  /* single entry input ring */
    uint32_t out[2]; /* single entry output ring (consists of two words) */
};

/*
 * According to CAAM docs max number of descriptors in single sequence is 64
 * You can chain them though.
 */
#define MAX_DSC_NUM 64

struct caam_job {
    uint32_t dsc[MAX_DSC_NUM]; /* job descriptors */
    uint32_t dsc_used;         /* number of filled entries */
    uint32_t status;           /* job result */
};

static struct caam_job_rings* g_rings;
static struct caam_job* g_job;
static bool caam_ready = false;

static void setup_job_rings(void) {
    paddr_t g_rings_pa;

    /* Initialize job ring addresses */
    memset(g_rings, 0, sizeof(*g_rings));
    g_rings_pa = vaddr_to_paddr((void *)g_rings);

    writel((uint32_t)g_rings_pa + offsetof(struct caam_job_rings, in),
           CAAM_IRBAR);  // input ring address
    writel((uint32_t)g_rings_pa + offsetof(struct caam_job_rings, out),
           CAAM_ORBAR);  // output ring address

    /* Initialize job ring sizes */
    writel(countof(g_rings->in), CAAM_IRSR);
    writel(countof(g_rings->in), CAAM_ORSR);
}

#ifdef MACH_IMX8ULP
uint32_t caam_jr0did_ms = 0;
uint32_t caam_jr0did_ls = 0;
uint32_t caam_jr1did_ms = 0;
uint32_t caam_jr1did_ls = 0;
uint32_t caam_jr2did_ms = 0;
uint32_t caam_jr2did_ls = 0;
uint32_t caam_jr3did_ms = 0;
uint32_t caam_jr3did_ls = 0;

uint32_t caam_irbar_jr0 = 0;
uint32_t caam_irbar_jr1 = 0;
uint32_t caam_irbar_jr2 = 0;
uint32_t caam_irbar_jr3 = 0;
uint32_t caam_orbar_jr0 = 0;
uint32_t caam_orbar_jr1 = 0;
uint32_t caam_orbar_jr2 = 0;
uint32_t caam_orbar_jr3 = 0;
uint32_t caam_irsr_jr0 = 0;
uint32_t caam_irsr_jr1 = 0;
uint32_t caam_irsr_jr2 = 0;
uint32_t caam_irsr_jr3 = 0;
uint32_t caam_orsr_jr0 = 0;
uint32_t caam_orsr_jr1 = 0;
uint32_t caam_orsr_jr2 = 0;
uint32_t caam_orsr_jr3 = 0;

void save_caam_regs(void)
{
    caam_jr0did_ms = readl(CAAM_JR0DID_MS);
    caam_jr0did_ls = readl(CAAM_JR0DID_LS);
    caam_jr1did_ms = readl(CAAM_JR1DID_MS);
    caam_jr1did_ls = readl(CAAM_JR1DID_MS);
    caam_jr2did_ms = readl(CAAM_JR2DID_MS);
    caam_jr2did_ls = readl(CAAM_JR2DID_MS);
    caam_jr3did_ms = readl(CAAM_JR3DID_MS);
    caam_jr3did_ls = readl(CAAM_JR3DID_MS);

    caam_irbar_jr0 = readl(CAAM_IRBAR_JR0);
    caam_irbar_jr1 = readl(CAAM_IRBAR_JR1);
    caam_irbar_jr2 = readl(CAAM_IRBAR_JR2);
    caam_irbar_jr3 = readl(CAAM_IRBAR_JR3);

    caam_orbar_jr0 = readl(CAAM_ORBAR_JR0);
    caam_orbar_jr1 = readl(CAAM_ORBAR_JR1);
    caam_orbar_jr2 = readl(CAAM_ORBAR_JR2);
    caam_orbar_jr3 = readl(CAAM_ORBAR_JR3);

    caam_irsr_jr0 = readl(CAAM_IRSR_JR0);
    caam_irsr_jr1 = readl(CAAM_IRSR_JR1);
    caam_irsr_jr2 = readl(CAAM_IRSR_JR2);
    caam_irsr_jr3 = readl(CAAM_IRSR_JR3);

    caam_orsr_jr0 = readl(CAAM_ORSR_JR0);
    caam_orsr_jr1 = readl(CAAM_ORSR_JR1);
    caam_orsr_jr2 = readl(CAAM_ORSR_JR2);
    caam_orsr_jr3 = readl(CAAM_ORSR_JR3);
}

void restore_caam_regs(void)
{
    writel(caam_jr0did_ms, CAAM_JR0DID_MS);
    writel(caam_jr0did_ls, CAAM_JR0DID_LS);
    writel(caam_jr1did_ms, CAAM_JR1DID_MS);
    writel(caam_jr1did_ls, CAAM_JR1DID_LS);
    writel(caam_jr2did_ms, CAAM_JR2DID_MS);
    writel(caam_jr2did_ls, CAAM_JR2DID_LS);
    writel(caam_jr3did_ms, CAAM_JR3DID_MS);
    writel(caam_jr3did_ls, CAAM_JR3DID_LS);

    writel(caam_irbar_jr0, CAAM_IRBAR_JR0);
    writel(caam_irbar_jr1, CAAM_IRBAR_JR1);
    writel(caam_irbar_jr2, CAAM_IRBAR_JR2);
    writel(caam_irbar_jr3, CAAM_IRBAR_JR3);

    writel(caam_orbar_jr0, CAAM_ORBAR_JR0);
    writel(caam_orbar_jr1, CAAM_ORBAR_JR1);
    writel(caam_orbar_jr2, CAAM_ORBAR_JR2);
    writel(caam_orbar_jr3, CAAM_ORBAR_JR3);

    writel(caam_irsr_jr0, CAAM_IRSR_JR0);
    writel(caam_irsr_jr1, CAAM_IRSR_JR1);
    writel(caam_irsr_jr2, CAAM_IRSR_JR2);
    writel(caam_irsr_jr3, CAAM_IRSR_JR3);

    writel(caam_orsr_jr0, CAAM_ORSR_JR0);
    writel(caam_orsr_jr1, CAAM_ORSR_JR1);
    writel(caam_orsr_jr2, CAAM_ORSR_JR2);
    writel(caam_orsr_jr3, CAAM_ORSR_JR3);
}
#endif

#ifndef MACH_IMX8Q
void caam_set_did(void) {
    /* The JR0 is assigned to non-secure world by default in ATF, assign
     * it to secure world here. */
    uint32_t cfg_ms = 0;
    uint32_t cfg_ls = 0;

#ifdef MACH_IMX8ULP
    cfg_ms = 0x7 << 0;  /* JRxDID_MS_PRIM_DID */
#else
    cfg_ms = 0x1 << 0;  /* JRxDID_MS_PRIM_DID */
#endif

    cfg_ms |= (0x1UL << 4) | (0x1 << 15); /* JRxDID_MS_PRIM_TZ | JRxDID_MS_TZ_OWN */
    cfg_ms |= (0x1UL << 16); /* JRxDID_MS_AMTD */
    cfg_ms |= (0x1UL << 19); /* JRxDID_MS_PRIM_ICID */
    cfg_ms |= (0x1UL << 31); /* JRxDID_MS_LDID */
    cfg_ms |= (0x1UL << 17); /* JRxDID_MS_LAMTD */

    writel(cfg_ms, CAAM_JRMIDR);
    writel(cfg_ls, CAAM_JRLIDR);
}

static int jr_reset(void)
{
    /*
     * Function reset the Job Ring HW
     * Reset is done in 2 steps:
     *  - Flush all pending jobs (Set RESET bit)
     *  - Reset the Job Ring (Set RESET bit second time)
     */
    u16 timeout = 10000;
    u32 reg_val;

    /* Mask interrupts to poll for reset completion status */
    reg_val = readl(CAAM_JRCFGR_LS) | BM_JRCFGR_LS_IMSK;
    writel(reg_val, CAAM_JRCFGR_LS);

    /* Initiate flush (required prior to reset) */
    writel(JRCR_RESET, CAAM_JRCR);
    do {
        reg_val = readl(CAAM_JRINTR);
        reg_val &= BM_JRINTR_HALT;
    } while ((reg_val == JRINTR_HALT_ONGOING) && --timeout);

    if (!timeout  || reg_val != JRINTR_HALT_DONE) {
        TLOGE("Failed to flush job ring\n");
        return -1;
    }

    /* Initiate reset */
    timeout = 100;
    writel(JRCR_RESET, CAAM_JRCR);
    do {
        reg_val = readl(CAAM_JRCR);
    } while ((reg_val & JRCR_RESET) && --timeout);

    if (!timeout) {
        TLOGE("Failed to reset job ring\n");
        return -1;
    }

    return 0;
}
#endif

static void run_job(struct caam_job* job) {
    uint32_t job_pa;
    uint32_t timeout = 10000000;

    /* prepare dma job */
    job_pa = vaddr_to_paddr(job->dsc);
    arch_clean_cache_range((addr_t)(job->dsc), job->dsc_used * sizeof(uint32_t));

    /* Add job to input ring */
    g_rings->out[0] = 0;
    g_rings->out[1] = 0;
    g_rings->in[0] = job_pa;
    arch_clean_cache_range((addr_t)g_rings, sizeof(*g_rings));

    /* start job */
    writel(1, CAAM_IRJAR);

    /* Wait for job ring to complete the job: 1 completed job expected */
    while ((readl(CAAM_ORSFR) != 1) && (--timeout))
        ;

    if (!timeout)
        panic("CAAM run_job timeout!\n");

    arch_clean_invalidate_cache_range((addr_t)g_rings->out, sizeof(g_rings->out));

    /* check that descriptor address is the one expected in the out ring */
    assert(g_rings->out[0] == job_pa);

    job->status = g_rings->out[1];

    /* remove job */
    writel(1, CAAM_ORJRR);
}

#ifndef MACH_IMX8Q
static void kick_trng(u32 ent_delay)
{
    u32 samples  = 512; /* number of bits to generate and test */
    u32 mono_min = 195;
    u32 mono_max = 317;
    u32 mono_range  = mono_max - mono_min;
    u32 poker_min = 1031;
    u32 poker_max = 1600;
    u32 poker_range = poker_max - poker_min + 1;
    u32 retries    = 2;
    u32 lrun_max   = 32;
    s32 run_1_min   = 27;
    s32 run_1_max   = 107;
    s32 run_1_range = run_1_max - run_1_min;
    s32 run_2_min   = 7;
    s32 run_2_max   = 62;
    s32 run_2_range = run_2_max - run_2_min;
    s32 run_3_min   = 0;
    s32 run_3_max   = 39;
    s32 run_3_range = run_3_max - run_3_min;
    s32 run_4_min   = -1;
    s32 run_4_max   = 26;
    s32 run_4_range = run_4_max - run_4_min;
    s32 run_5_min   = -1;
    s32 run_5_max   = 18;
    s32 run_5_range = run_5_max - run_5_min;
    s32 run_6_min   = -1;
    s32 run_6_max   = 17;
    s32 run_6_range = run_6_max - run_6_min;
    u32 val;

    /* Put RNG in program mode */
    /* Setting both RTMCTL:PRGM and RTMCTL:TRNG_ACC causes TRNG to
     * properly invalidate the entropy in the entropy register and
     * force re-generation.
     */
    val = readl(CAAM_RTMCTL) | RTMCTL_PGM | RTMCTL_ACC;
    writel(val, CAAM_RTMCTL);

    /* Configure the RNG Entropy Delay
     * Performance-wise, it does not make sense to
     * set the delay to a value that is lower
     * than the last one that worked (i.e. the state handles
     * were instantiated properly. Thus, instead of wasting
     * time trying to set the values controlling the sample
     * frequency, the function simply returns.
     */
    val = readl(CAAM_RTSDCTL);
    val &= BM_TRNG_ENT_DLY;
    val >>= BS_TRNG_ENT_DLY;
    if (ent_delay < val) {
        /* Put RNG4 into run mode */
        val = readl(CAAM_RTMCTL);
        val &= ~(RTMCTL_PGM | RTMCTL_ACC);
        writel(val, CAAM_RTMCTL);
        return;
    }

    val = (ent_delay << BS_TRNG_ENT_DLY) | samples;
    writel(val, CAAM_RTSDCTL);

    /*
     * Recommended margins (min,max) for freq. count:
     *   freq_mul = RO_freq / TRNG_clk_freq
     *   rtfrqmin = (ent_delay x freq_mul) >> 1;
     *   rtfrqmax = (ent_delay x freq_mul) << 3;
     * Given current deployments of CAAM in i.MX SoCs, and to simplify
     * the configuration, we consider [1,16] to be a safe interval
     * for the freq_mul and the limits of the interval are used to compute
     * rtfrqmin, rtfrqmax
     */
    writel(ent_delay >> 1, CAAM_RTFRQMIN);
    writel(ent_delay << 7, CAAM_RTFRQMAX);

    writel((retries << 16) | lrun_max, CAAM_RTSCMISC);
    writel(poker_max, CAAM_RTPKRMAX);
    writel(poker_range, CAAM_RTPKRRNG);
    writel((mono_range << 16) | mono_max, CAAM_RTSCML);
    writel((run_1_range << 16) | run_1_max, CAAM_RTSCR1L);
    writel((run_2_range << 16) | run_2_max, CAAM_RTSCR2L);
    writel((run_3_range << 16) | run_3_max, CAAM_RTSCR3L);
    writel((run_4_range << 16) | run_4_max, CAAM_RTSCR4L);
    writel((run_5_range << 16) | run_5_max, CAAM_RTSCR5L);
    writel((run_6_range << 16) | run_6_max, CAAM_RTSCR6PL);

    val = readl(CAAM_RTMCTL);
    /*
     * Select raw sampling in both entropy shifter
     * and statistical checker
     */
    val &= ~BM_TRNG_SAMP_MODE;
    val |= TRNG_SAMP_MODE_RAW_ES_SC;
    /* Put RNG4 into run mode */
    val &= ~(RTMCTL_PGM | RTMCTL_ACC);
    /*test with sample mode only */
    writel(val, CAAM_RTMCTL);

    /* Clear the ERR bit in RTMCTL if set. The TRNG error can occur when the
     * RNG clock is not within 1/2x to 8x the system clock.
     * This error is possible if ROM code does not initialize the system PLLs
     * immediately after PoR.
     */
    /* setbits_le32(CAAM_RTMCTL, RTMCTL_ERR); */
}

static void do_clear_rng_error(void)
{
    u32 val;

    val = readl(CAAM_RTMCTL);

    if (val & (RTMCTL_ERR | RTMCTL_FCT_FAIL)) {
        val = readl(CAAM_RTMCTL) | RTMCTL_ERR;
        writel(val, CAAM_RTMCTL);
        val = readl(CAAM_RTMCTL);
    }
}

/*
 *  Descriptors to instantiate SH0, SH1, load the keys
 */
static const u32 rng_inst_sh0_desc[] = {
    /* Header, don't setup the size */
    CAAM_HDR_CTYPE | CAAM_HDR_ONE | CAAM_HDR_START_INDEX(0),
    /* Operation instantiation (sh0) */
    CAAM_PROTOP_CTYPE | CAAM_C1_RNG | ALGO_RNG_SH(0) | ALGO_RNG_PR |
    ALGO_RNG_INSTANTIATE,
};

static const u32 rng_inst_sh1_desc[] = {
    /* wait for done - Jump to next entry */
    CAAM_C1_JUMP | CAAM_JUMP_LOCAL | CAAM_JUMP_TST_ALL_COND_TRUE | CAAM_JUMP_OFFSET(1),
    /* Clear written register (write 1) */
    CAAM_C0_LOAD_IMM | CAAM_DST_CLEAR_WRITTEN | sizeof(u32),
    0x00000001,
    /* Operation instantiation (sh1) */
    CAAM_PROTOP_CTYPE | CAAM_C1_RNG | ALGO_RNG_SH(1) | ALGO_RNG_PR | ALGO_RNG_INSTANTIATE,
};

static const u32 rng_inst_load_keys[] = {
    /* wait for done - Jump to next entry */
    CAAM_C1_JUMP | CAAM_JUMP_LOCAL | CAAM_JUMP_TST_ALL_COND_TRUE | CAAM_JUMP_OFFSET(1),
    /* Clear written register (write 1) */
    CAAM_C0_LOAD_IMM | CAAM_DST_CLEAR_WRITTEN | sizeof(u32),
    0x00000001,
    /* Generate the Key */
    CAAM_PROTOP_CTYPE | CAAM_C1_RNG | BM_ALGO_RNG_SK | ALGO_RNG_GENERATE,
};

static void do_inst_desc(u32 *desc, u32 status)
{
    u32 *pdesc = desc;
    u8  desc_len;
    bool add_sh0   = false;
    bool add_sh1   = false;
    bool load_keys = false;

    /*
     * Modify the the descriptor to remove if necessary:
     *  - The key loading
     *  - One of the SH already instantiated
     */
    desc_len = RNG_DESC_SH0_SIZE;
    if ((status & RDSTA_IF0) != RDSTA_IF0)
        add_sh0 = true;

    if ((status & RDSTA_IF1) != RDSTA_IF1) {
        add_sh1 = true;
        if (add_sh0)
            desc_len += RNG_DESC_SH1_SIZE;
    }

    if ((status & RDSTA_SKVN) != RDSTA_SKVN) {
        load_keys = true;
        desc_len += RNG_DESC_KEYS_SIZE;
    }

    /* Copy the SH0 descriptor anyway */
    memcpy(pdesc, rng_inst_sh0_desc, sizeof(rng_inst_sh0_desc));
    pdesc += RNG_DESC_SH0_SIZE;

    if (load_keys) {
        TLOGE("RNG - Load keys\n");
        memcpy(pdesc, rng_inst_load_keys, sizeof(rng_inst_load_keys));
        pdesc += RNG_DESC_KEYS_SIZE;
    }

    if (add_sh1) {
        if (add_sh0) {
            TLOGE("RNG - Instantiation of SH0 and SH1\n");
            /* Add the sh1 descriptor */
            memcpy(pdesc, rng_inst_sh1_desc, sizeof(rng_inst_sh1_desc));
        } else {
            TLOGE("RNG - Instantiation of SH1 only\n");
            /* Modify the SH0 descriptor to instantiate only SH1 */
            desc[1] &= ~BM_ALGO_RNG_SH;
            desc[1] |= ALGO_RNG_SH(1);
        }
    }

    /* Setup the descriptor size */
    desc[0] &= ~(0x3F);
    desc[0] |= CAAM_HDR_DESCLEN(desc_len);
    g_job->dsc_used = desc_len;
}

static int do_instantiation(void)
{
    int ret = -1;
    u32 ent_delay;
    u32 status;

    ent_delay = TRNG_SDCTL_ENT_DLY_MIN;

    do {
        /* Read the CAAM RNG status */
        status = readl(CAAM_RDSTA);

        if ((status & RDSTA_IF0) != RDSTA_IF0) {
            /* Configure the RNG entropy delay */
            kick_trng(ent_delay);
            ent_delay += 400;
        }

        do_clear_rng_error();

        if ((status & (RDSTA_IF0 | RDSTA_IF1)) != (RDSTA_IF0 | RDSTA_IF1)) {
            /* Prepare the instantiation descriptor */
            do_inst_desc(g_job->dsc, status);

            /* Run Job */
            run_job(g_job);
        } else {
            ret = 0;
            TLOGE("RNG instantiation done (%d)\n", ent_delay);
            goto end_instantation;
        }
    } while (ent_delay < TRNG_SDCTL_ENT_DLY_MAX);

    TLOGE("RNG Instantation Failure - Entropy delay (%d)\n", ent_delay);
    ret = -1;

end_instantation:
    return ret;
}

static void rng_init(void)
{
    int  ret;

    ret = jr_reset();
    if (ret != 0) {
        TLOGE("Error CAAM JR reset\n");
        return;
    }

    ret = do_instantiation();

    if (ret != 0)
        TLOGE("Error do_instantiation\n");

    jr_reset();

    return;
}

static void do_cfg_jrqueue(void)
{
    u32 value = 0;

    /* Configure the HW Job Rings */
    setup_job_rings();

    value = readl(CAAM_JRINTR) | JRINTR_JRI;
    writel(value, CAAM_JRINTR);

    /*
     * Configure interrupts but disable it:
     * Optimization to generate an interrupt either when there are
     * half of the job done or when there is a job done and
     * 10 clock cycles elapse without new job complete
     */
    value = 10 << BS_JRCFGR_LS_ICTT;
    value |= (1 << BS_JRCFGR_LS_ICDCT) & BM_JRCFGR_LS_ICDCT;
    value |= BM_JRCFGR_LS_ICEN;
    value |= BM_JRCFGR_LS_IMSK;
    writel(value, CAAM_JRCFGR_LS);

    /* Enable deco watchdog */
    value = readl(CAAM_MCFGR) | BM_MCFGR_WDE;
    writel(value, CAAM_MCFGR);
}

/*!
 * Initialize the CAAM.
 *
 */
void caam_open(void)
{
    u32 temp_reg;
    u32 init_mask;

    /* reset the CAAM */
    temp_reg = readl(CAAM_MCFGR) | CAAM_MCFGR_DMARST | CAAM_MCFGR_SWRST;
    writel(temp_reg,  CAAM_MCFGR);
    while (readl(CAAM_MCFGR) & CAAM_MCFGR_DMARST)
          ;

#ifdef MACH_IMX8ULP
    restore_caam_regs();
#endif

    jr_reset();
    do_cfg_jrqueue();

    /* Check if the RNG is already instantiated */
    temp_reg = readl(CAAM_RDSTA);
    init_mask = RDSTA_IF0 | RDSTA_IF1 | RDSTA_SKVN;
    if ((temp_reg & init_mask) == init_mask) {
        TLOGE("RNG already instantiated 0x%X\n", temp_reg);
        return;
    }

    rng_init();
}
#endif

void init_caam_env(uint level) {

    /* allocate rings */
    g_rings = memalign(16, sizeof(struct caam_job_rings));
    if (!g_rings) {
        panic("out of memory allocating rings\n");
    }

    /* allocate jobs */
    g_job = memalign(MAX_DSC_NUM * sizeof(uint32_t), sizeof(struct caam_job));
    if (!g_job) {
        panic("out of memory allocating job\n");
    }

#ifndef MACH_IMX8Q
    caam_set_did();
#endif

    /* secure memory address */
    sram_base = (uint8_t *)CAAM_ARB_BASE_ADDR;

    /* Initialize job ring addresses */
    setup_job_rings();

    /* caam is ready now */
    caam_ready = true;
}

static uint8_t entropy[32] __attribute__((aligned(64))) = {
        0x0f, 0x0e, 0x0d, 0x0c, 0x0b, 0x0a, 0x09, 0x08,
        0x07, 0x06, 0x05, 0x04, 0x03, 0x02, 0x01, 0x00,
        0x1f, 0x1e, 0x1d, 0x1c, 0x1b, 0x1a, 0x19, 0x18,
        0x17, 0x16, 0x15, 0x14, 0x13, 0x12, 0x11, 0x10
};

void imx_trusty_rand_add_entropy(const void *buf, size_t len) {
    if (len == 0)
        return;

    uint32_t enp = 0;
    for (size_t i = 0; i < len; i++) {
        enp ^= ((enp << 8) | (enp >> 24)) ^ ((const uint8_t *)buf)[i];
    }

    ((uint32_t *)entropy)[0] = enp;
}

static struct mutex lock = MUTEX_INITIAL_VALUE(lock);

static int imx_rand(bool enable_pr) {
    int rand_num, *rand_buf;
    paddr_t ptr, entropy_pa;
    lk_time_ns_t ts = 0;

    /* use software rand before rng hardware ready */
    if (!caam_ready) {
        ts = current_time_ns();
        srand((uint32_t)ts);
        rand_num = rand();
        return rand_num;
    }

    rand_buf = memalign(64, sizeof(int));
    if (!rand_buf) {
        TLOGE("Out-of-memory error, will return fixed value!\n");
        return (int)((unsigned int)12345 * 1664525 + 1013904223);
    }

    mutex_acquire(&lock);

    ptr = vaddr_to_paddr(rand_buf);
    entropy_pa = vaddr_to_paddr(entropy);
    arch_clean_cache_range((addr_t)(rand_buf), sizeof(int));
    arch_clean_cache_range((addr_t)(entropy), sizeof(entropy));

    g_job->dsc[0] = 0xB0800006;
    g_job->dsc[1] = 0x12200020;
    g_job->dsc[2] = (uint32_t)entropy_pa;
    if (enable_pr)
        g_job->dsc[3] = 0x82500802;
    else
        g_job->dsc[3] = 0x82500800;
    g_job->dsc[4] = 0x60340000 | (0x0000ffff & sizeof(int));
    g_job->dsc[5] = (uint32_t)ptr;
    g_job->dsc_used = 6;

    run_job(g_job);

    mutex_release(&lock);

    if (g_job->status & JOB_RING_STS) {
        TLOGE("job failed (0x%08x), will return fixed value!\n", g_job->status);
        free(rand_buf);
        return (int)((unsigned int)12345 * 1664525 + 1013904223);
    }
    arch_clean_invalidate_cache_range((addr_t)(rand_buf), sizeof(int));

    rand_num = *rand_buf;
    free(rand_buf);
    return rand_num;
}

void platform_random_get_bytes(uint8_t *buf, size_t len) {
    while (len) {
        uint32_t val = (uint32_t)imx_rand(false);
        size_t todo = len;
        for (size_t i = 0; i < sizeof(val) && i < todo; i++, len--) {
            *buf++ = val & 0xff;
            val >>= 8;
        }
   }
};

static void caam_derive_pr_rng(uint8_t* out, size_t requested) {
    while (requested) {
        uint32_t val = (uint32_t)imx_rand(true);
        size_t todo = requested;
        for (size_t i = 0; i < sizeof(val) && i < todo; i++, requested--) {
            *out++ = val & 0xff;
            val >>= 8;
        }
    }
}

static int derive_pr_rng(user_addr_t user_ptr) {
    uint8_t *buf = NULL;
    int ret = 0;
    struct pr_rng_msg msg;

    ret = copy_from_user(&msg, user_ptr, sizeof(struct pr_rng_msg));
    if (unlikely(ret != 0)) {
        TLOGE("%s: failed to copy data from user!\n", __func__ );
        return -1;
    }

    buf = malloc(msg.len);
    if (!buf) {
        TLOGE("%s: failed to malloc memory!\n", __func__);
        return -1;
    }

    caam_derive_pr_rng(buf, msg.len);
    ret = copy_to_user((user_addr_t)(msg.buf), buf, msg.len);
    if (unlikely(ret != 0)) {
        TLOGE("%s: failed to copy data to user!\n", __func__ );
        ret = -1;
        goto exit;
    }
    ret = 0;

exit:
    if (buf)
        free(buf);

    return ret;
}

/* Use CAAM to encapsulate blob, all input/output buffer
 * address should be physical address.
 */
static int caam_gen_blob(user_addr_t user_ptr) {
    struct gen_blob_msg msg;
    int ret;

    if (!caam_ready) {
        TLOGE("caam not ready!\n");
        return CAAM_FAILURE;
    }

    ret = copy_from_user(&msg, user_ptr, sizeof(struct gen_blob_msg));
    if (unlikely(ret != 0)) {
        TLOGE("%s: failed to copy data from user!\n", __func__ );
        return CAAM_FAILURE;
    }

    mutex_acquire(&lock);

    g_job->dsc[0] = 0xB0800008;
    g_job->dsc[1] = 0x14400010;
    g_job->dsc[2] = msg.kmod_paddr;
    g_job->dsc[3] = 0xF0000000 | (0x0000ffff & (msg.size));
    g_job->dsc[4] = msg.plain_paddr;
    g_job->dsc[5] = 0xF8000000 | (0x0000ffff & (msg.size + CAAM_KB_HEADER_LEN));
    g_job->dsc[6] = msg.blob_paddr;
    g_job->dsc[7] = 0x870D0000;
    g_job->dsc_used = 8;

    run_job(g_job);

    mutex_release(&lock);

    if (g_job->status & JOB_RING_STS) {
        TLOGE("job failed (0x%08x)\n", g_job->status);
        return CAAM_FAILURE;
    }

    return CAAM_SUCCESS;
}

static int caam_decap_blob(user_addr_t user_ptr) {
    struct decap_blob_msg msg;
    int ret;

    if (!caam_ready) {
        TLOGE("caam not ready!\n");
        return CAAM_FAILURE;
    }

    ret = copy_from_user(&msg, user_ptr, sizeof(struct decap_blob_msg));
    if (unlikely(ret != 0)) {
        TLOGE("%s: failed to copy data from user!\n", __func__ );
        return CAAM_FAILURE;
    }

    mutex_acquire(&lock);

    g_job->dsc[0] = 0xB0800008;
    g_job->dsc[1] = 0x14400010;
    g_job->dsc[2] = msg.kmod_paddr;
    g_job->dsc[3] = 0xF0000000 | (0x0000ffff & (msg.size + CAAM_KB_HEADER_LEN));
    g_job->dsc[4] = msg.blob_paddr;
    g_job->dsc[5] = 0xF8000000 | (0x0000ffff & (msg.size));
    g_job->dsc[6] = msg.plain_paddr;
    g_job->dsc[7] = 0x860D0000;
    g_job->dsc_used = 8;

    run_job(g_job);

    mutex_release(&lock);

    if (g_job->status & JOB_RING_STS) {
        TLOGE("job failed (0x%08x)\n", g_job->status);
        return CAAM_FAILURE;
    }

    return CAAM_SUCCESS;
}

static int caam_gen_kdfv1_root_key(user_addr_t user_ptr) {
    struct kdfv1_msg msg;
    paddr_t pa;
    uint8_t *buf;
    int ret;

    ret = copy_from_user(&msg, user_ptr, sizeof(struct kdfv1_msg));
    if (unlikely(ret != 0)) {
        TLOGE("%s: failed to copy data from user!\n", __func__ );
        return CAAM_FAILURE;
    }

    buf = memalign(64, msg.size);
    if (!buf) {
        TLOGE("Out-of-memory error!\n");
        ret = CAAM_FAILURE;
        goto exit;
    }

    mutex_acquire(&lock);

    pa = vaddr_to_paddr(buf);

    arch_clean_cache_range((addr_t)(buf), msg.size);
    /*
     * This sequence uses caam blob generation protocol in
     * master key verification mode to generate unique for device
     * persistent 256-bit sequence that we will be using a root key
     * for our key derivation function v1. This is the only known way
     * on this platform of producing persistent unique device key that
     * does not require persistent storage. Dsc[2..5] effectively contains
     * 16 bytes of randomly generated salt that gets mixed (among other
     * things) with device master key to produce result.
     */
    g_job->dsc[0] = 0xB080000B;
    g_job->dsc[1] = 0x14C00010;
    g_job->dsc[2] = 0x7083A393; /* salt word 0 */
    g_job->dsc[3] = 0x2CC0C9F7; /* salt word 1 */
    g_job->dsc[4] = 0xFC5D2FC0; /* salt word 2 */
    g_job->dsc[5] = 0x2C4B04E7; /* salt word 3 */
    g_job->dsc[6] = 0xF0000000;
    g_job->dsc[7] = 0;
    g_job->dsc[8] = 0xF8000030;
    g_job->dsc[9] = pa;
    g_job->dsc[10] = 0x870D0002;
    g_job->dsc_used = 11;

    run_job(g_job);

    mutex_release(&lock);

    if (g_job->status & JOB_RING_STS) {
        TLOGE("job failed (0x%08x)\n", g_job->status);
        ret = CAAM_FAILURE;
        goto exit;
    }

    arch_clean_invalidate_cache_range((addr_t)(buf), msg.size);

    ret = copy_to_user((user_addr_t)(msg.out), buf, msg.size);
    if (unlikely(ret != 0)) {
        TLOGE("%s: failed to copy data to user!\n", __func__ );
        ret = CAAM_FAILURE;
        goto exit;
    }

    ret = CAAM_SUCCESS;

exit:
    if (buf)
        free(buf);

    return ret;
}

static int caam_aes_op_pa(struct aes_msg msg) {
    mutex_acquire(&lock);

    /*
     * Now AES key use aeskey.
     * aeskey is derived from the first 16 bytes of RPMB key.
     */
    g_job->dsc[0] = 0xb0800008;
    g_job->dsc[1] = 0x02000010;
    g_job->dsc[2] = msg.key_paddr;
    g_job->dsc[3] = msg.enc ? 0x8210020D : 0x8210020C;
    g_job->dsc[4] = 0x22120000 | (0x0000ffff & msg.len);
    g_job->dsc[5] = msg.in_paddr;
    g_job->dsc[6] = 0x60300000 | (0x0000ffff & msg.len);
    g_job->dsc[7] = msg.out_paddr;
    g_job->dsc_used = 8;

    run_job(g_job);

    mutex_release(&lock);

    if (g_job->status & JOB_RING_STS) {
       TLOGE("job failed (0x%08x)\n", g_job->status);
        return CAAM_FAILURE;
    }

    return CAAM_SUCCESS;

}

static int caam_aes_op(user_addr_t user_ptr) {
    struct aes_msg msg;
    int ret = 0;

    if (!caam_ready) {
        TLOGE("caam not ready!\n");
        return CAAM_FAILURE;
    }

    ret = copy_from_user(&msg, user_ptr, sizeof(struct aes_msg));
    if (unlikely(ret != 0)) {
        TLOGE("%s: failed to copy data from user!\n", __func__ );
        return CAAM_FAILURE;
    }

    if (caam_aes_op_pa(msg)) {
        TLOGE("aes failed\n");
        return CAAM_FAILURE;
    }
    return CAAM_SUCCESS;
}

static int caam_hash_pa(struct caam_hash_msg msg) {
    mutex_acquire(&lock);

    if (msg.len < 0xffff) {
        g_job->dsc[0] = 0xB0800006;

        if (msg.algo == SHA1)
            g_job->dsc[1] = 0x8441000D;
        else
            g_job->dsc[1] = 0x8443000D;

        g_job->dsc[2] = 0x24140000 | (0x0000ffff & msg.len);
        g_job->dsc[3] = msg.in_paddr;

        if (msg.algo == SHA1)
            g_job->dsc[4] = 0x54200000 | 20;
        else
            g_job->dsc[4] = 0x54200000 | 32;

        g_job->dsc[5] = msg.out_paddr;
        g_job->dsc_used = 6;
    } else {
        g_job->dsc[0] = 0xB0800007;

        if (msg.algo == SHA1)
            g_job->dsc[1] = 0x8441000D;
        else
            g_job->dsc[1] = 0x8443000D;

        g_job->dsc[2] = 0x24540000;
        g_job->dsc[3] = msg.in_paddr;
        g_job->dsc[4] = msg.len;

        if (msg.algo == SHA1)
            g_job->dsc[5] = 0x54200000 | 20;
        else
            g_job->dsc[5] = 0x54200000 | 32;

        g_job->dsc[6] = msg.out_paddr;
        g_job->dsc_used = 7;
    }

    run_job(g_job);

    mutex_release(&lock);

    if (g_job->status & JOB_RING_STS) {
        TLOGE("job failed (0x%08x)\n", g_job->status);
        return CAAM_FAILURE;
    }

    return CAAM_SUCCESS;
}

static int caam_hash(user_addr_t user_ptr) {
    struct caam_hash_msg msg;
    int ret = 0;

    ret = copy_from_user(&msg, user_ptr, sizeof(struct caam_hash_msg));
    if (unlikely(ret != 0)) {
        TLOGE("%s: failed to copy data from user!\n", __func__ );
        return CAAM_FAILURE;
    }

    if (caam_hash_pa(msg)) {
        TLOGE("hash failed\n");
        return CAAM_FAILURE;
    }

    return CAAM_SUCCESS;
}

/* AES ECB CBC descriptor template*/
static const uint32_t aes_decriptor_template_ecb_cbc[] = {
  /* 00 */ 0xB0800000, /* HEADER */
  /* 01 */ 0x02000000, /* KEY */
  /* 02 */ 0x00000000, /* place: key address */
  /* 03 */ 0x12200010, /* LOAD 16 bytes of iv to Class 1 Context Register */
  /* 04 */ 0x00000000, /* place: iv address */
  /* 05 */ 0x23130000, /* FIFO LOAD Message via SGT */
  /* 06 */ 0x00000000, /* place: source address */
  /* 07 */ 0x61300000, /* FIFO STORE Message via SGT */
  /* 08 */ 0x00000000, /* place: destination address */
  /* 09 */ 0x82100000, /* OPERATION: AES Decrypt, AS = zeroes,*/
  /* 10 */ 0x52200010, /* STORE IV from Class 1 Context Register offset 0 bytes.*/
  /* 11 */ 0x00000000, /* place: iv address */
};

static int caam_aes_cbc_pa(struct aes_cbc_ctr_msg msg) {
    mutex_acquire(&lock);

    memcpy(g_job->dsc, aes_decriptor_template_ecb_cbc, sizeof(aes_decriptor_template_ecb_cbc));

    g_job->dsc[0] = 0xB080000C;
    g_job->dsc[1] |= ((uint32_t)msg.key_size & 0x3FF);
    g_job->dsc[2] = msg.key_paddr;
    g_job->dsc[4] = msg.iv_paddr;
    g_job->dsc[5] |= ((uint32_t)msg.size & 0x0000FFFFu);
    g_job->dsc[6] = msg.input_text_sg_paddr;
    g_job->dsc[7] |= ((uint32_t)msg.size & 0x0000FFFFu);
    g_job->dsc[8] = msg.output_text_sg_paddr;
    g_job->dsc[9] |= ALGORITHM_OPERATION_CMD_AAI_CBC;
    if (CIPHER_ENCRYPT == msg.enc_flag)
        g_job->dsc[9] |= CIPHER_ENCRYPT; /* add ENC bit to specify Encrypt OPERATION */
    g_job->dsc[11] = msg.iv_paddr;

    g_job->dsc_used = 12;
    run_job(g_job);

    mutex_release(&lock);

    if (g_job->status & JOB_RING_STS) {
        TLOGE("job failed (0x%08x)\n", g_job->status);
        return CAAM_FAILURE;
    }
    return CAAM_SUCCESS;
}

static int caam_aes_cbc(user_addr_t user_ptr) {
    struct aes_cbc_ctr_msg msg;
    int ret;

    ret = copy_from_user(&msg, user_ptr, sizeof(struct aes_cbc_ctr_msg));
    if (unlikely(ret != 0)) {
        TLOGE("%s: failed to copy data from user!\n", __func__ );
        return CAAM_FAILURE;
    }

    if (caam_aes_cbc_pa(msg)) {
        TLOGE("failed for CAAM!\n");
        return CAAM_FAILURE;
    }
    return CAAM_SUCCESS;
}

static int caam_aes_ecb_pa(struct aes_ecb_msg msg) {
    mutex_acquire(&lock);

    memcpy(g_job->dsc, aes_decriptor_template_ecb_cbc, sizeof(aes_decriptor_template_ecb_cbc));

    g_job->dsc[0] = 0xB080000A;
    g_job->dsc[1] |= ((uint32_t)msg.key_size & 0x3FF);
    g_job->dsc[2] = msg.key_paddr;
    /*ECB has no context, jump to current index + 2 = 6 (FIFO LOAD)*/
    g_job->dsc[3] = 0xA0000002u;
    g_job->dsc[5] |= ((uint32_t)msg.size & 0x0000FFFFu);
    g_job->dsc[6] = msg.input_text_sg_paddr;
    g_job->dsc[7] |= ((uint32_t)msg.size & 0x0000FFFFu);
    g_job->dsc[8] = msg.output_text_sg_paddr;
    g_job->dsc[9] |= ALGORITHM_OPERATION_CMD_AAI_ECB;
    if (CIPHER_ENCRYPT == msg.enc_flag)
        g_job->dsc[9] |= CIPHER_ENCRYPT; /* add ENC bit to specify Encrypt OPERATION */

    g_job->dsc_used = 10;
    run_job(g_job);

    mutex_release(&lock);

    if (g_job->status & JOB_RING_STS) {
        TLOGE("job failed (0x%08x)\n", g_job->status);
        return CAAM_FAILURE;
    }

    return CAAM_SUCCESS;
}

static int caam_aes_ecb(user_addr_t user_ptr) {
    struct aes_ecb_msg msg;
    int ret;

    ret = copy_from_user(&msg, user_ptr, sizeof(struct aes_ecb_msg));
    if (unlikely(ret != 0)) {
        TLOGE("%s: failed to copy data from user!\n", __func__ );
        return CAAM_FAILURE;
    }

    if (!((msg.key_size == 16) || ((msg.key_size == 24)) || ((msg.key_size == 32)))) {
        TLOGE("invaild parameter!\n");
        return CAAM_FAILURE;
    }
    /* ECB mode, size must be non-zero 16-byte multiple */
    if (msg.size % 16) {
        TLOGE("invaild parameter!\n");
        return CAAM_FAILURE;
    }

    if (caam_aes_ecb_pa(msg)) {
        TLOGE("failed for CAAM!\n");
        return CAAM_FAILURE;
    }

    return CAAM_SUCCESS;
}

static const uint32_t aes_decriptor_template_ctr[] = {
  /* 00 */ 0xB0800000, /* HEADER */
  /* 01 */ 0x02000000, /* KEY */
  /* 02 */ 0x00000000, /* place: key address */
  /* 03 */ 0x12201010, /* LOAD 16 bytes of CTR0 to Class 1 Context Register. Offset 16 bytes. */
  /* 04 */ 0x00000000, /* place: CTR0 address */

  /* 05 */ 0x82100000, /* OPERATION: AES CTR (de)crypt in Update mode */
  /* 06 */ 0x23130000, /* FIFO LOAD Message */
  /* 07 */ 0x00000000, /* place: source address */
  /* 08 */ 0x61300000, /* FIFO STORE Message */
  /* 09 */ 0x00000000, /* place: destination address */

  /* 10 */ 0xA2000001, /* JMP always to next command. Done checkpoint (wait for Class 1 Done) */
  /* 11 */ 0x10880004, /* LOAD Immediate to Clear Written Register. */
  /* 12 */ 0x08000004, /* value for Clear Written Register: C1D and C1DS bits are set */
  /* 13 */ 0x22930010, /* FIFO LOAD Message Immediate 16 bytes */
  /* 14 */ 0x00000000, /* all zeroes 0-3 */

  /* 15 */ 0x00000000, /* all zeroes 4-7 */
  /* 16 */ 0x00000000, /* all zeroes 8-11 */
  /* 17 */ 0x00000000, /* all zeroes 12-15 */
  /* 18 */ 0x60300010, /* FIFO STORE Message 16 bytes */
  /* 19 */ 0x00000000, /* place: nonce_last[] block address */

  /* 20 */ 0x82100000, /* OPERATION: AES CTR (de)crypt in Update mode */
  /* 21 */ 0x52201010, /* STORE 16 bytes of CTRi from Class 1 Context Register offset 16 bytes. */
  /* 22 */ 0x00000000, /* place: CTRi address */
};

static int caam_aes_ctr_pa(struct aes_cbc_ctr_msg msg, uint32_t ecount_buf, uint32_t num_left) {
    mutex_acquire(&lock);

    memcpy(g_job->dsc, aes_decriptor_template_ctr, sizeof(aes_decriptor_template_ctr));

    g_job->dsc[0] = 0xB0800017;
    /* If the key is encrypted, this is the decrypted length of the key material only. */
    g_job->dsc[1] |= ((uint32_t)msg.key_size & 0x3FF);
    g_job->dsc[2] = msg.key_paddr;
    /* g_job->dsc[3] configures 16 bytes length for CTR0 in aes_ctr_decriptor_template */
    g_job->dsc[4] = msg.iv_paddr;

    g_job->dsc[6] |= ((uint32_t)msg.size & 0x0000FFFFu);
    g_job->dsc[7] = msg.input_text_sg_paddr;
    g_job->dsc[8] |= ((uint32_t)msg.size & 0x0000FFFFu);
    g_job->dsc[9] = msg.output_text_sg_paddr;

    if (num_left == 0) {
        g_job->dsc[10] = 0xA000000B; /* jump to current index + 11 (=21) */
    } else {
        g_job->dsc[5] |= 0x08u; /* finalize will not update the latest CTR*/
    }
    g_job->dsc[19] = ecount_buf;
    g_job->dsc[22] = msg.iv_paddr;
    /* read last CTRi from AES back to caller */
    g_job->dsc_used = 23;

    run_job(g_job);

    mutex_release(&lock);

    if (g_job->status & JOB_RING_STS) {
        TLOGE("job failed (0x%08x)\n", g_job->status);
        return CAAM_FAILURE;
    }
    return CAAM_SUCCESS;
}

static int caam_aes_ctr(user_addr_t user_ptr) {
    struct aes_cbc_ctr_msg msg;
    int ret;

    ret = copy_from_user(&msg, user_ptr, sizeof(struct aes_cbc_ctr_msg));
    if (unlikely(ret != 0)) {
        TLOGE("%s: failed to copy data from user!\n", __func__ );
        return CAAM_FAILURE;
    }

    if (caam_aes_ctr_pa(msg, 0, 0)) {
        TLOGE("failed for CAAM!\n");
        return CAAM_FAILURE;
    }
    return CAAM_SUCCESS;
}

/*AES GCM mode*/
static const uint32_t aes_decriptor_template_gcm[] = {
   /* 00 */ 0xB0800000, /* HEADER */
   /* 01 */ 0x02000000, /* KEY */
   /* 02 */ 0x00000000, /* place: key address */

   /* 03 */ 0x82100900, /* OPERATION: AES GCM Encrypt Update */

   /* 04 */ 0x12200000, /* lOAD context to Class 1 context offset 0 tag */
   /* 05 */ 0x00000000, /* place: context address */

   /* 06 */ 0xA0000002, /* Jump to next 2 command */
   /* 07 */ 0x00000000, /* NULL */

   /* 08 */ 0xA0000004, /* Jump to next 4 command */
   /* 09 */ 0x00000000, /* NULL*/

   /* 10 */ 0xA0000002, /* Jump to next 2 command */
   /* 11 */ 0x00000000, /* NULL */

   /* 12 */ 0x00000000, /* NULL*/
   /* 13 */ 0x00000000, /* NULL*/

   /* 14 */ 0x52200000, /* STORE from Class 1 context to tag */
   /* 15 */ 0x00000000, /* place: context address */
};

static int caam_aes_gcm_pa(struct aes_gcm_msg msg) {
    bool ctx_save_flag, finialize_flag;

    if (CIPHER_ENCRYPT == msg.enc_flag) {
        ctx_save_flag = 1;
        finialize_flag = 1;
    } else {
        ctx_save_flag = 0;
        finialize_flag = 1;
    }

    mutex_acquire(&lock);

    memcpy(g_job->dsc, aes_decriptor_template_gcm, sizeof(aes_decriptor_template_gcm));

    /* key address and key size */
    g_job->dsc[1] |= (msg.key_size & 0x3FFu);
    g_job->dsc[2] = msg.key_paddr;

    /* Encrypt/Decrypt */
    if (CIPHER_ENCRYPT == msg.enc_flag) {
        g_job->dsc[3] |= CIPHER_ENCRYPT; /* ENC */
    } else {
        g_job->dsc[3] |= 0x1 << 1; /* ICV check */
    }

    if(finialize_flag == 1) {
        g_job->dsc[3] |= 0x2 << 2;
    }

    g_job->dsc[4] = 0x22210000, /* FIFO LOAD IV (flush) */
    g_job->dsc[4] |= (msg.iv_size & 0xFFu);
    g_job->dsc[5] = msg.iv_paddr;

    if (msg.text_size == 0  && msg.aad_size == 0) {
        g_job->dsc[4] |= 0x00020000u; /* LC1 */
    }

    /* AAD address and size */
    if(msg.aad_size) {
        g_job->dsc[6] = 0x23310000, /* FIFO LOAD AAD (flush) */
        g_job->dsc[6] |= (msg.aad_size & 0x0000FFFF);
        if (msg.text_size == 0) {
            g_job->dsc[6] |= 0x00020000u; /* LC1 */
        }
        g_job->dsc[7] = msg.aad_sg_paddr; /* place: AAD address */
    }

    if(msg.text_size || finialize_flag) {
        /* source address and size */
        g_job->dsc[8] = 0x23110000, /* FIFO LOAD SGT message(flush last) */
        g_job->dsc[8] |= (msg.text_size & 0x0000FFFF);

        g_job->dsc[9] = msg.input_text_sg_paddr;

        if (CIPHER_DECRYPT == msg.enc_flag) {
            g_job->dsc[10] = 0x223b0000 | (msg.tag_size & 0xFFu);
            g_job->dsc[11] = msg.tag_paddr;
        } else
            g_job->dsc[8] |= 0x00020000u; /* LC1 */

        g_job->dsc[12] = 0x61300000, /* FIFO STORE SGT Message */
        /* destination address and size */
        g_job->dsc[12] |= (msg.text_size & 0x0000FFFF);

        g_job->dsc[13] = msg.output_text_sg_paddr;
    }

    if (ctx_save_flag) {
        g_job->dsc[14] |= (msg.tag_size & 0xFFu);
        g_job->dsc[15] = msg.tag_paddr;
        g_job->dsc[0] = 0xB0800010;
        g_job->dsc_used = 16;
    } else {
        g_job->dsc[0] = 0xB080000E;
        g_job->dsc_used = 14;
    }

    /* schedule the job */
    run_job(g_job);

    mutex_release(&lock);

    if (g_job->status & JOB_RING_STS) {
        TLOGE("job failed (0x%08x)\n", g_job->status);
        return CAAM_FAILURE;
    }

    return CAAM_SUCCESS;
}

static int caam_aes_gcm(user_addr_t user_ptr) {
    struct aes_gcm_msg msg;
    int ret;

    ret = copy_from_user(&msg, user_ptr, sizeof(struct aes_gcm_msg));
    if (unlikely(ret != 0)) {
        TLOGE("%s: failed to copy data from user!\n", __func__ );
        return CAAM_FAILURE;
    }

    if (caam_aes_gcm_pa(msg)) {
        TLOGE("failed for CAAM!\n");
        return CAAM_FAILURE;
    }

    return CAAM_SUCCESS;
}

/* DES EDE */
static const uint32_t des_decriptor_template_ede_cbc[] = {
  /* 00 */ 0xB0800000, /* HEADER */
  /* 01 */ 0x02000000, /* KEY */
  /* 02 */ 0x00000000, /* place: key address */
  /* 03 */ 0x12200008, /* LOAD 8 bytes of iv to Class 1 Context Register */
  /* 04 */ 0x00000000, /* place: iv address */
  /* 05 */ 0x23130000, /* FIFO LOAD Message via SGT */
  /* 06 */ 0x00000000, /* place: SGT address */
  /* 07 */ 0x61300000, /* FIFO STORE Message via SGT */
  /* 08 */ 0x00000000, /* place: SGT address */
  /* 09 */ 0x82200000, /* OPERATION: DES Decrypt, AS = zeroes, AAI = zeroes (CTR) */
  /* 10 */ 0x52200008, /* STORE IV from Class 1 Context Register offset 0 bytes. */
  /* 11 */ 0x00000000, /* place: iv address */
};

static int caam_tdes_ecb_pa(struct tdes_ecb_msg msg) {
    mutex_acquire(&lock);

    memcpy(g_job->dsc, des_decriptor_template_ede_cbc, sizeof(des_decriptor_template_ede_cbc));

    g_job->dsc[0] = 0xB080000A;
    g_job->dsc[1] |= msg.key_size;
    g_job->dsc[2] = msg.key_paddr;
    g_job->dsc[3] = 0xA0000002u; /* ECB has no context, jump to current index + 2 = 5 (FIFO LOAD) */
    g_job->dsc[5] |= (msg.text_size & 0x0000FFFF);
    g_job->dsc[6] = msg.input_text_sg_paddr;
    g_job->dsc[7] |= (msg.text_size & 0x0000FFFFu);
    g_job->dsc[8] = msg.output_text_sg_paddr;
    g_job->dsc[9] |= ALGORITHM_OPERATION_CMD_AAI_ECB; /* AAI = 20h */
    if (CIPHER_ENCRYPT == msg.enc_flag)
        g_job->dsc[9] |= CIPHER_ENCRYPT; /*  add ENC bit to specify Encrypt OPERATION */

    g_job->dsc[9] |= ALGORITHM_OPERATION_ALGSEL_3DES;    /* 3DES */

    g_job->dsc_used = 10;

    /* schedule the job */
    run_job(g_job);

    mutex_release(&lock);

    if (g_job->status & JOB_RING_STS) {
        TLOGE("job failed (0x%08x)\n", g_job->status);
        return CAAM_FAILURE;
    }

    return CAAM_SUCCESS;
}

static int caam_tdes_ecb(user_addr_t user_ptr) {
    struct tdes_ecb_msg msg;
    int ret;

    ret = copy_from_user(&msg, user_ptr, sizeof(struct tdes_ecb_msg));
    if (unlikely(ret != 0)) {
        TLOGE("%s: failed to copy data from user!\n", __func__ );
        return CAAM_FAILURE;
    }

    if (caam_tdes_ecb_pa(msg)) {
        TLOGE("failed for CAAM!\n");
        return CAAM_FAILURE;
    }

    return CAAM_SUCCESS;
}

static int caam_tdes_cbc_pa(struct tdes_cbc_msg msg) {
    mutex_acquire(&lock);

    memcpy(g_job->dsc, des_decriptor_template_ede_cbc, sizeof(des_decriptor_template_ede_cbc));

    g_job->dsc[0] = 0xB080000C;
    g_job->dsc[1] |= msg.key_size;
    g_job->dsc[2] = msg.key_paddr;
    g_job->dsc[4] = msg.iv_paddr;
    g_job->dsc[5] |= (msg.text_size & 0x0000FFFFu);
    g_job->dsc[6] = msg.input_text_sg_paddr;
    g_job->dsc[7] |= (msg.text_size & 0x0000FFFFu);
    g_job->dsc[8] = msg.output_text_sg_paddr;
    g_job->dsc[9] |= ALGORITHM_OPERATION_CMD_AAI_CBC; /* AAI = 20h */
    if (CIPHER_ENCRYPT == msg.enc_flag)
        g_job->dsc[9] |= CIPHER_ENCRYPT; /*  add ENC bit to specify Encrypt OPERATION */
    g_job->dsc[9] |= ALGORITHM_OPERATION_ALGSEL_3DES;    /* 3DES */
    g_job->dsc[11] = msg.iv_paddr;
    g_job->dsc_used = 12;

    /* schedule the job */
    run_job(g_job);

    mutex_release(&lock);

    if (g_job->status & JOB_RING_STS) {
        TLOGE("job failed (0x%08x)\n", g_job->status);
        return CAAM_FAILURE;
    }

    return CAAM_SUCCESS;
}

static int caam_tdes_cbc(user_addr_t user_ptr) {
    struct tdes_cbc_msg msg;
    int ret;

    ret = copy_from_user(&msg, user_ptr, sizeof(struct tdes_cbc_msg));
    if (unlikely(ret != 0)) {
        TLOGE("%s: failed to copy data from user!\n", __func__ );
        return CAAM_FAILURE;
    }

    if (caam_tdes_cbc_pa(msg)) {
        TLOGE("failed for CAAM!\n");
        return CAAM_FAILURE;
    }

    return CAAM_SUCCESS;
}

#if defined(MACH_IMX8MQ) || defined(MACH_IMX8MM) || defined(MACH_IMX8MP)
/* DEK BLOB */
static const uint32_t decriptor_template_gen_dek_blob [] = {
  /* 00 */ 0xB0800000, /* HEADER */
  /* 01 */ 0x14C00C08, /* Key Modifier load, 8 bytes */
  /* 02 */ 0x00005566, /* Key Modifier 1 - | Length of the payload | AES - 0x55 | CCM - 0x66 | */
  /* 03 */ 0x00000000, /* Key Modifier 2 - | 0 |*/
  /* 04 */ 0xF0000000, /* CMD_SEQ_IN_TYPE | SEQ_LENGTH(len) */
  /* 05 */ 0x00000000, /* Input physical address */
  /* 06 */ 0xF8000000, /* CMD_SEQ_OUT_TYPE | SEQ_LENGTH(len) */
  /* 07 */ 0x00000000, /* Output physical address */
  /* 08 */ 0x870D0008, /* BLOB_ENCAPS | PROT_BLOB_SEC_MEM */
};

static const uint32_t decriptor_template_decap_dek_blob [] = {
  /* 00 */ 0xB0800000, /* HEADER */
  /* 01 */ 0x14C00C08, /* Key Modifier load, 8 bytes */
  /* 02 */ 0x00005566, /* Key Modifier 1 - | Length of the payload | AES - 0x55 | CCM - 0x66 | */
  /* 03 */ 0x00000000, /* Key Modifier 2 - | 0 |*/
  /* 04 */ 0xF0000000, /* CMD_SEQ_IN_TYPE | SEQ_LENGTH(len) */
  /* 05 */ 0x00000000, /* Input physical address */
  /* 06 */ 0xF8000000, /* CMD_SEQ_OUT_TYPE | SEQ_LENGTH(len) */
  /* 07 */ 0x00000000, /* Output physical address */
  /* 08 */ 0x860D0008, /* BLOB_DECAPS | PROT_BLOB_SEC_MEM */
};

static uint32_t issue_cmd(unsigned int page, unsigned int partition, unsigned int cmd) {
    unsigned int status = 0;
    uint32_t timeout = 10000000;

    /* Send cmd */
    writel(SMCR_PAGE(page) | SMCR_PRTN(partition) | SMCR_CMD(cmd), CAAM_SMCR);

    /* wait the command to complete */
    do {
        status = readl(CAAM_SMCSR);
    } while ((SMCSR_CERR(status) == SMCSR_CERR_NOT_COMPLETED) && (--timeout));

    if (!timeout)
        TLOGE("timeout when waiting for SMCSR status(%d)!\n", status);

    return status;
}

static uint32_t sm_deallocate_pages(uint32_t partition, uint32_t page, uint32_t nb_page)
{
    uint32_t status = 0;
    uint32_t page_t;

    for (page_t = page; page_t < page + nb_page; page_t++) {
        /* De-Allocate page - partition not used */
        status = issue_cmd(page_t, 0, SMCR_PAGE_DEALLOC);
        if (SMCSR_AERR(status) != SMCSR_AERR_NO_ERROR) {
            TLOGE("Failed to free pages!\n");
            return CAAM_FAILURE;
        }
    }

    return CAAM_SUCCESS;
}

static uint32_t sm_deallocate_partition(uint32_t partition) {
    uint32_t status = 0;

    if (SMPO_OWNER(readl(CAAM_SMPO), partition) != SMPO_PO_OWNED) {
        TLOGE("The partition is not owned by used JR!\n");
        return CAAM_FAILURE;
    }

    status = issue_cmd(0, partition, SMCR_PARTITION_DEALLOC);
    if (SMCSR_AERR(status) != SMCSR_AERR_NO_ERROR) {
        TLOGE("Free partition failed!\n");
        return CAAM_FAILURE;
    }

    return CAAM_SUCCESS;
}

static uint32_t sm_alloc_pages(uint32_t partition, uint32_t page, uint32_t nb_page) {
    unsigned int status = 0;
    unsigned int page_t;

    /* make sure the partition is free */
    if (SMPO_OWNER(readl(CAAM_SMPO), partition) != SMPO_PO_AVAIL) {
        TLOGE("partition is not free!\n");
        return CAAM_FAILURE;
    }

    /* enable all Secure Memory permission to all groups */
    writel(SMAPR_GRP1(UINT8_MAX) | SMAPR_GRP2(UINT8_MAX), SMAPR(partition));
    writel(UINT32_MAX, SMAG2(partition));
    writel(UINT32_MAX, SMAG1(partition));

    /* allocate page */
    for (page_t = page; page_t < page + nb_page; page_t++) {
        /* first we check if all pages are available */
        status = issue_cmd(page_t, 0, SMCR_PAGE_INQ);
        if (SMCSR_PO(status) != SMCSR_PO_AVAILABLE) {
            TLOGE("Not all pages are free!\n");
            return CAAM_FAILURE;
        }
    }

    for (page_t = page; page_t < page + nb_page; page_t++) {
        /* Allocate page to partition */
        status = issue_cmd(page_t, partition, SMCR_PAGE_ALLOC);
        if (SMCSR_AERR(status) != SMCSR_AERR_NO_ERROR) {
            TLOGE("Allocate pages failed!\n");
            return CAAM_FAILURE;
        }
    }

    for (page_t = page; page_t < page + nb_page; page_t++) {
        /* Check if the page is available, else return on error */
        status = issue_cmd(page_t, 0, SMCR_PAGE_INQ);
        if (SMCSR_PO(status) != SMCSR_PO_OWNED ||
            SMCSR_PRTN(status) != partition) {
            TLOGE("Check pages status failed!\n");
            return CAAM_FAILURE;
        }
    }

    return CAAM_SUCCESS;
}

static uint32_t sm_set_access_perm(uint32_t partition) {

    /* make sure we owned the partition */
    if (SMPO_OWNER(readl(CAAM_SMPO), partition) != SMPO_PO_OWNED) {
        TLOGE("The partition is not owned by used JR!\n");
        return CAAM_FAILURE;
    }

    /* set the secure group 1 secure memory permission */
    writel((0x1 << 1), SMAG1(partition));
    writel(0, SMAG2(partition));
    writel((SMAPR_GRP1(GRP_BLOB) | SMAPR_GRP2(0) | SMAPR_CSP |
            SMAPR_SMAP_LCK | SMAPR_SMAG_LCK), SMAPR(partition));

    return CAAM_SUCCESS;
}

static int caam_gen_dek_blob_pa(uint8_t *dek_buf, uint8_t *dek_blob_buf, struct gen_dek_blob_msg msg) {
    /* we use partition 1, page 3 in secure memory to align with ROM */
    paddr_t dek_pa, blob_pa;
    uint32_t partition = 1, page = 3, nb_page = 1;
    struct hab_dek_blob_header *hdr = NULL;
    uint8_t *tmp_blob;
    uint32_t ret = CAAM_FAILURE;

    if (!dek_buf || !dek_blob_buf) {
        TLOGE("Wrong input parameter!\n");
        return CAAM_FAILURE;
    }

    if ((msg.dek_size != 16) && (msg.dek_size != 24) && (msg.dek_size != 32)) {
        TLOGE("Unsupported DEK size!\n");
        return CAAM_FAILURE;
    }

    if (sm_alloc_pages(partition, page, nb_page) != CAAM_SUCCESS)
        return CAAM_FAILURE;

    /* copy the dek to secure memory and flush cache */
    memcpy((void *)SEC_MEM_PAGE3, dek_buf, msg.dek_size);
    arch_clean_cache_range((addr_t)(SEC_MEM_PAGE3), msg.dek_size);
    arch_clean_invalidate_cache_range((addr_t)(SEC_MEM_PAGE3), msg.dek_size);

    sm_set_access_perm(partition);

    tmp_blob = memalign(64, msg.dek_size + CAAM_KB_HEADER_LEN);
    if (!tmp_blob) {
        TLOGE("Out-of-memory error!\n");
        ret = CAAM_FAILURE;
        goto exit;
    }

    memset(tmp_blob, 0, msg.dek_size + CAAM_KB_HEADER_LEN);

    mutex_acquire(&lock);

    dek_pa = vaddr_to_paddr((void *)SEC_MEM_PAGE3);
    blob_pa = vaddr_to_paddr(tmp_blob);

    arch_clean_cache_range((addr_t)(tmp_blob), msg.dek_size + CAAM_KB_HEADER_LEN);

    memcpy(g_job->dsc, decriptor_template_gen_dek_blob, sizeof(decriptor_template_gen_dek_blob));
    g_job->dsc[0] = 0xB0800009;
    g_job->dsc[2] |= (((msg.dek_size) & (0xFFFF)) << 16);
    g_job->dsc[4] |= ((msg.dek_size) & (0xFFFF));
    g_job->dsc[5] = (uint32_t)(unsigned long)(dek_pa);
    g_job->dsc[6] |= ((msg.dek_size + CAAM_KB_HEADER_LEN) & (0xFFFF));
    g_job->dsc[7] = blob_pa;

    /* schedule the job */
    g_job->dsc_used = 9;
    run_job(g_job);

    mutex_release(&lock);

    if (g_job->status & JOB_RING_STS) {
        TLOGE("job failed (0x%08x)\n", g_job->status);
        ret = CAAM_FAILURE;
        goto exit;
    }

    /* construct the header */
    hdr = (struct hab_dek_blob_header *)dek_blob_buf;
    hdr->tag = HAB_HDR_TAG;
    hdr->len_msb = 0x00;
    hdr->len_lsb = msg.dek_size + CAAM_KB_HEADER_LEN + sizeof(*hdr);
    hdr->par = HAB_HDR_V4;
    hdr->mode = HAB_HDR_MODE_CCM;
    hdr->alg = HAB_HDR_ALG_AES;
    hdr->size = msg.dek_size;
    hdr->flg = 0x00;

    memcpy(dek_blob_buf + sizeof(struct hab_dek_blob_header), tmp_blob, msg.dek_size + CAAM_KB_HEADER_LEN);

    arch_clean_invalidate_cache_range((addr_t)(dek_blob_buf), msg.dek_size + CAAM_KB_HEADER_LEN + \
                                       sizeof(struct hab_dek_blob_header));

    ret = CAAM_SUCCESS;

exit:
    if (tmp_blob != NULL)
        free(tmp_blob);

    /* free pags and partition */
    sm_deallocate_pages(partition, page, nb_page);
    sm_deallocate_partition(partition);

    return ret;
}

static int caam_gen_dek_blob(user_addr_t user_ptr) {
    struct gen_dek_blob_msg msg;
    uint8_t *dek_buf, *dek_blob_buf;
    int ret;

    ret = copy_from_user(&msg, user_ptr, sizeof(struct gen_dek_blob_msg));
    if (unlikely(ret != 0)) {
        TLOGE("%s: failed to copy data from user!\n", __func__ );
        return CAAM_FAILURE;
    }

    dek_buf = memalign(64, msg.dek_size);
    dek_blob_buf = memalign(64, msg.dek_size + CAAM_KB_HEADER_LEN + sizeof(struct hab_dek_blob_header));
    if (!dek_buf || !dek_blob_buf) {
        TLOGE("Out-of-memory error!\n");
        ret = CAAM_FAILURE;
        goto exit;
    }

    ret = copy_from_user(dek_buf, (user_addr_t)(msg.dek), msg.dek_size);
    if (unlikely(ret != 0)) {
        TLOGE("%s: failed to copy data from user!\n", __func__ );
        ret = CAAM_FAILURE;
        goto exit;
    }

    if (caam_gen_dek_blob_pa(dek_buf, dek_blob_buf, msg)) {
        TLOGE("aes failed\n");
        ret = CAAM_FAILURE;
        goto exit;
    }

    ret = copy_to_user((user_addr_t)(msg.dek_blob), dek_blob_buf,
                       msg.dek_size + CAAM_KB_HEADER_LEN + sizeof(struct hab_dek_blob_header));
    if (unlikely(ret != 0)) {
        TLOGE("%s: failed to copy data to user!\n", __func__ );
        ret = CAAM_FAILURE;
        goto exit;
    }

    ret = CAAM_SUCCESS;

exit:
    if (dek_buf)
        free(dek_buf);
    if (dek_blob_buf)
        free(dek_blob_buf);

    return ret;
}

static int caam_decap_dek_blob_pa(uint8_t *dek_buf, uint8_t *dek_blob_buf, struct decap_dek_blob_msg msg) {
    paddr_t dek_pa, blob_pa;
    /* we use partition 1, page 3 in secure memory to align with ROM */
    uint32_t partition = 1, page = 3, nb_page = 1;
    uint32_t blob_size = 0, dek_size = 0;
    void *tmp_blob = NULL;
    uint32_t ret = CAAM_FAILURE;

    if (!dek_buf || !msg.dek_blob_size || !dek_blob_buf) {
        TLOGE("Wrong input parameter!\n");
        return CAAM_FAILURE;
    }

    if ((msg.dek_blob_size != 72) && (msg.dek_blob_size != 80) && (msg.dek_blob_size != 88)) {
        TLOGE("Unsupported DEK BLOBsize!\n");
        return CAAM_FAILURE;
    }

    if (sm_alloc_pages(partition, page, nb_page) != CAAM_SUCCESS)
        return CAAM_FAILURE;

    /* allocate cache aligned buffer for input buffer */
    blob_size = msg.dek_blob_size - sizeof(struct hab_dek_blob_header);
    tmp_blob = memalign(64, blob_size);
    if (!tmp_blob) {
        TLOGE("failed to allocate dek blob memory!\n");
        ret = CAAM_FAILURE;
        goto exit;
    }
    memcpy(tmp_blob, dek_blob_buf + sizeof(struct hab_dek_blob_header), blob_size);

    mutex_acquire(&lock);

    dek_pa = vaddr_to_paddr((void *)SEC_MEM_PAGE3);
    blob_pa = vaddr_to_paddr(tmp_blob);

    arch_clean_cache_range((addr_t)(tmp_blob), blob_size);
    arch_clean_cache_range((addr_t)(dek_buf), msg.dek_blob_size - CAAM_KB_HEADER_LEN - \
                            sizeof(struct hab_dek_blob_header));

    /* the dek should be in secure memory */
    dek_size = blob_size - CAAM_KB_HEADER_LEN;
    memset((void *)SEC_MEM_PAGE3, 0, dek_size);

    sm_set_access_perm(partition);

    /* construct the descriptors */
    memcpy(g_job->dsc, decriptor_template_decap_dek_blob, sizeof(decriptor_template_decap_dek_blob));
    g_job->dsc[0] = 0xB0800009;
    g_job->dsc[2] |= (((dek_size) & (0xFFFF)) << 16);
    g_job->dsc[4] |= ((blob_size) & (0xFFFF));
    g_job->dsc[5] = (uint32_t)(unsigned long)(blob_pa);
    g_job->dsc[6] |= ((dek_size) & (0xFFFF));
    g_job->dsc[7] = dek_pa;

    /* schedule the job */
    g_job->dsc_used = 9;
    run_job(g_job);

    mutex_release(&lock);

    if (g_job->status & JOB_RING_STS) {
        TLOGE("job failed (0x%08x), FADR (0x%08x)\n", g_job->status, readl(CAAM_FADR));
        ret = CAAM_FAILURE;
        goto exit;
    }

    arch_clean_invalidate_cache_range((addr_t)(dek_buf), msg.dek_blob_size - CAAM_KB_HEADER_LEN - \
                                       sizeof(struct hab_dek_blob_header));

    ret = CAAM_SUCCESS;

exit:
    if (tmp_blob != NULL)
        free(tmp_blob);

    /* free pags and partition */
    sm_deallocate_pages(partition, page, nb_page);
    sm_deallocate_partition(partition);

    return ret;
}

static int caam_decap_dek_blob(user_addr_t user_ptr) {
    struct decap_dek_blob_msg msg;
    uint8_t *dek_buf, *dek_blob_buf;
    int ret;

    ret = copy_from_user(&msg, user_ptr, sizeof(struct decap_dek_blob_msg));
    if (unlikely(ret != 0)) {
        TLOGE("%s: failed to copy data from user!\n", __func__ );
        return CAAM_FAILURE;
    }

    dek_buf = memalign(64, msg.dek_blob_size - CAAM_KB_HEADER_LEN - sizeof(struct hab_dek_blob_header));
    dek_blob_buf = memalign(64, msg.dek_blob_size);
    if (!dek_buf || !dek_blob_buf) {
        TLOGE("Out-of-memory error!\n");
        ret = CAAM_FAILURE;
        goto exit;
    }

    ret = copy_from_user(dek_blob_buf, (user_addr_t)(msg.dek_blob), msg.dek_blob_size);
    if (unlikely(ret != 0)) {
        TLOGE("%s: failed to copy data from user!\n", __func__ );
        ret = CAAM_FAILURE;
        goto exit;
    }

    if (caam_decap_dek_blob_pa(dek_buf, dek_blob_buf, msg)) {
        TLOGE("decap dek blob failed\n");
        ret = CAAM_FAILURE;
        goto exit;
    }

    ret = copy_to_user((user_addr_t)(msg.dek), dek_buf, msg.dek_blob_size - CAAM_KB_HEADER_LEN - \
                        sizeof(struct hab_dek_blob_header));
    if (unlikely(ret != 0)) {
        TLOGE("%s: failed to copy data to user!\n", __func__ );
        ret = CAAM_FAILURE;
        goto exit;
    }

    ret = CAAM_SUCCESS;

exit:
    if (dek_buf)
        free(dek_buf);
    if (dek_blob_buf)
        free(dek_blob_buf);

    return ret;
}
#endif /* defined(MACH_IMX8MQ) || defined(MACH_IMX8MM) || defined(MACH_IMX8MP) */

static int caam_gen_bkek_key_pa(uint8_t *kmod_buf, uint8_t *out_buf, struct gen_bkek_key_msg msg) {
    paddr_t kmod_pa, out_pa;

    mutex_acquire(&lock);

    kmod_pa = vaddr_to_paddr(kmod_buf);
    out_pa = vaddr_to_paddr(out_buf);

    arch_clean_cache_range((addr_t)(kmod_buf), msg.kmod_size);
    arch_clean_cache_range((addr_t)(out_buf), msg.out_size);

    g_job->dsc[0] = 0xB0800006;
    g_job->dsc[1] = 0x14400010;
    g_job->dsc[2] = kmod_pa;
    g_job->dsc[3] = 0xF8000020;
    g_job->dsc[4] = out_pa;
    g_job->dsc[5] = 0x870D0002;
    g_job->dsc_used = 6;

    run_job(g_job);

    mutex_release(&lock);

    if (g_job->status & JOB_RING_STS) {
        TLOGE("job failed (0x%08x)\n", g_job->status);
        return CAAM_FAILURE;
    }

    arch_clean_invalidate_cache_range((addr_t)(out_buf), msg.out_size);

    return CAAM_SUCCESS;
}

static int caam_gen_bkek_key(user_addr_t user_ptr) {
    struct gen_bkek_key_msg msg;
    uint8_t *kmod_buf, *out_buf;
    int ret;

    ret = copy_from_user(&msg, user_ptr, sizeof(struct gen_bkek_key_msg));
    if (unlikely(ret != 0)) {
        TLOGE("%s: failed to copy data from user!\n", __func__ );
        return CAAM_FAILURE;
    }

    kmod_buf = memalign(64, msg.kmod_size);
    out_buf = memalign(64, msg.out_size);
    if (!kmod_buf || !out_buf) {
        TLOGE("Out-of-memory error!\n");
        ret = CAAM_FAILURE;
        goto exit;
    }

    ret = copy_from_user(kmod_buf, (user_addr_t)(msg.kmod), msg.kmod_size);
    if (unlikely(ret != 0)) {
        TLOGE("%s: failed to copy data from user!\n", __func__ );
        ret = CAAM_FAILURE;
        goto exit;
    }

    if (caam_gen_bkek_key_pa(kmod_buf, out_buf, msg)) {
        TLOGE("aes failed\n");
        ret = CAAM_FAILURE;
        goto exit;
    }

    ret = copy_to_user((user_addr_t)(msg.out), out_buf, msg.out_size);
    if (unlikely(ret != 0)) {
        TLOGE("%s: failed to copy data to user!\n", __func__ );
        ret = CAAM_FAILURE;
        goto exit;
    }

    ret = CAAM_SUCCESS;

exit:
    if (kmod_buf)
        free(kmod_buf);
    if (out_buf)
        free(out_buf);

    return ret;
}

static int caam_gen_mppubk_pa(uint8_t *out_buf, struct gen_mppubk_msg msg) {
    paddr_t out_pa;

    mutex_acquire(&lock);

    out_pa = vaddr_to_paddr(out_buf);

    arch_clean_cache_range((addr_t)(out_buf), msg.out_size);

    g_job->dsc[0] = 0xB0840005;
#ifdef MACH_IMX8Q
    g_job->dsc[1] = PDB_MP_CSEL_P384;
#else
    g_job->dsc[1] = PDB_MP_CSEL_P256;
#endif
    g_job->dsc[2] = (uint32_t)out_pa;
    g_job->dsc[3] = 64;
    g_job->dsc[4] = 0x86140000;
    g_job->dsc_used = 5;

    run_job(g_job);

    mutex_release(&lock);

    if (g_job->status & JOB_RING_STS) {
        TLOGE("job failed (0x%08x)\n", g_job->status);
        return CAAM_FAILURE;
    }

    arch_clean_invalidate_cache_range((addr_t)(out_buf), msg.out_size);

    return CAAM_SUCCESS;
}

uint32_t caam_gen_mppriv(void)
{
    uint32_t pa;
    char passphrase[30] = "manufacturing protection";

    mutex_acquire(&lock);

    pa = vaddr_to_paddr(passphrase);

    g_job->dsc[0] = 0xB0840005;
#ifdef MACH_IMX8Q
    g_job->dsc[1] = PDB_MP_CSEL_P384;
#else
    g_job->dsc[1] = PDB_MP_CSEL_P256;
#endif
    g_job->dsc[2] = pa;
    g_job->dsc[3] = strlen(passphrase);
    g_job->dsc[4] = 0x87140000;
    g_job->dsc_used = 5;

    run_job(g_job);

    mutex_release(&lock);

    if (g_job->status & JOB_RING_STS) {
        TLOGE("job failed (0x%08x)\n", g_job->status);
        return CAAM_FAILURE;
    }

    return CAAM_SUCCESS;
}

static int caam_gen_mppubk(user_addr_t user_ptr) {
    struct gen_mppubk_msg msg;
    uint8_t *out_buf;
    int ret;

#if IMX8M_OPEN_MPPUBK_DEBUG
    caam_gen_mppriv();
#endif

    ret = copy_from_user(&msg, user_ptr, sizeof(struct gen_mppubk_msg));
    if (unlikely(ret != 0)) {
        TLOGE("%s: failed to copy data from user!\n", __func__ );
        return CAAM_FAILURE;
    }

    out_buf = memalign(64, msg.out_size);
    if (!out_buf) {
        TLOGE("Out-of-memory error!\n");
        ret = CAAM_FAILURE;
        goto exit;
    }

    if (caam_gen_mppubk_pa(out_buf, msg)) {
        TLOGE("generate mppubk failed\n");
        ret = CAAM_FAILURE;
        goto exit;
    }

    ret = copy_to_user((user_addr_t)(msg.out), out_buf, msg.out_size);
    if (unlikely(ret != 0)) {
        TLOGE("%s: failed to copy data to user!\n", __func__ );
        ret = CAAM_FAILURE;
        goto exit;
    }

    ret = CAAM_SUCCESS;

exit:
    if (out_buf)
        free(out_buf);

    return ret;
}

static int caam_get_keybox(user_addr_t user_ptr) {
    struct keybox_msg msg;
    int ret;

    ret = copy_from_user(&msg, user_ptr, sizeof(struct keybox_msg));
    if (unlikely(ret != 0)) {
        TLOGE("%s: failed to copy data from user!\n", __func__ );
        return CAAM_FAILURE;
    }

    ret = copy_to_user((user_addr_t)(msg.kbox), sram_base, msg.kbox_size);
    if (unlikely(ret != 0)) {
        TLOGE("%s: failed to copy data to user!\n", __func__ );
        return CAAM_FAILURE;
    }

    return CAAM_SUCCESS;
}

static bool check_uuid_equal(const struct uuid* a, const struct uuid* b) {
    return !memcmp(a, b, sizeof(struct uuid));
}

#ifdef MACH_IMX8ULP
static bool caam_resume_flag = true;
static bool caam_suspend_flag = true;

static void imx_caam_resume_cpu(uint level)
{
    mutex_acquire(&lock);
    if (caam_resume_flag) {
        caam_open();
        caam_resume_flag = false;
        caam_suspend_flag = true;
    }
    mutex_release(&lock);
}

static void imx_caam_suspend_cpu(uint level)
{
    mutex_acquire(&lock);
    if (caam_suspend_flag) {
        save_caam_regs();
        caam_suspend_flag = false;
        caam_resume_flag = true;
    }
    mutex_release(&lock);
}

/*
 * CAAM on imx8ulp will lose power during suspend/resume,
 * init CAAM and do RNG instantiation during resume.
 */
LK_INIT_HOOK_FLAGS(imx_caam_resume_cpu, imx_caam_resume_cpu, LK_INIT_LEVEL_KERNEL - 1, LK_INIT_FLAG_CPU_RESUME);

LK_INIT_HOOK_FLAGS(imx_caam_suspend_cpu, imx_caam_suspend_cpu, LK_INIT_LEVEL_KERNEL - 1, LK_INIT_FLAG_CPU_SUSPEND);
#endif

LK_INIT_HOOK(imx_caam, init_caam_env, LK_INIT_LEVEL_KERNEL - 1);

static int32_t sys_caam_ioctl(uint32_t fd, uint32_t cmd, user_addr_t user_ptr) {
    struct rctee_app* app = current_rctee_app();
    CHECK_FD(fd);
    switch (cmd) {
        case CAAM_DERIVE_PR_RNG:
            return derive_pr_rng(user_ptr);
        case CAAM_GEN_BLOB:
            if (check_uuid_equal(&app->props.uuid, &hwcrypto_ta_uuid)) {
                return caam_gen_blob(user_ptr);
            } else {
                TLOGE("%s: unauthorized access!\n", __func__);
                return ERR_INVALID_ARGS;
            }
        case CAAM_DECAP_BLOB:
            if (check_uuid_equal(&app->props.uuid, &hwcrypto_ta_uuid)) {
                return caam_decap_blob(user_ptr);
            } else {
                TLOGE("%s: unauthorized access!\n", __func__);
                return ERR_INVALID_ARGS;
            }
#if defined(MACH_IMX8MQ) || defined(MACH_IMX8MM) || defined(MACH_IMX8MP)
        case CAAM_GEN_DEK_BLOB:
            if (check_uuid_equal(&app->props.uuid, &hwcrypto_ta_uuid)) {
                return caam_gen_dek_blob(user_ptr);
            } else {
                TLOGE("%s: unauthorized access!\n", __func__);
                return ERR_INVALID_ARGS;
            }
        case CAAM_DECAP_DEK_BLOB:
            if (check_uuid_equal(&app->props.uuid, &hwcrypto_ta_uuid)) {
                return caam_decap_dek_blob(user_ptr);
            } else {
                TLOGE("%s: unauthorized access!\n", __func__);
                return ERR_INVALID_ARGS;
            }
#endif
        case CAAM_DERIVE_KDFV1_ROOT_KEY:
            if (check_uuid_equal(&app->props.uuid, &hwcrypto_ta_uuid)) {
                return caam_gen_kdfv1_root_key(user_ptr);
            } else {
                TLOGE("%s: unauthorized access!\n", __func__);
                return ERR_INVALID_ARGS;
            }
        case CAAM_AES_OP:
            return caam_aes_op(user_ptr);
        case CAAM_HASH:
            return caam_hash(user_ptr);
        case CAAM_AES_CBC:
            return caam_aes_cbc(user_ptr);
        case CAAM_AES_ECB:
            return caam_aes_ecb(user_ptr);
        case CAAM_AES_CTR:
            return caam_aes_ctr(user_ptr);
        case CAAM_AES_GCM:
            return caam_aes_gcm(user_ptr);
        case CAAM_TDES_ECB:
            return caam_tdes_ecb(user_ptr);
        case CAAM_TDES_CBC:
            return caam_tdes_cbc(user_ptr);
        case CAAM_GEN_BKEK_KEY:
            if (check_uuid_equal(&app->props.uuid, &hwcrypto_ta_uuid)) {
                return caam_gen_bkek_key(user_ptr);
            } else {
                TLOGE("%s: unauthorized access!\n", __func__);
                return ERR_INVALID_ARGS;
            }
        case CAAM_GEN_MPPUBK:
            if (check_uuid_equal(&app->props.uuid, &hwcrypto_ta_uuid)) {
                return caam_gen_mppubk(user_ptr);
            } else {
                TLOGE("%s: unauthorized access!\n", __func__);
                return ERR_INVALID_ARGS;
            }
        case CAAM_GET_KEYBOX:
            if (check_uuid_equal(&app->props.uuid, &hwcrypto_ta_uuid)) {
                return caam_get_keybox(user_ptr);
            } else {
                TLOGE("%s: unauthorized access!\n", __func__);
                return ERR_INVALID_ARGS;
            }
        default:
            TLOGE("%s: invalid caam syscall!\n", __func__);
            return ERR_INVALID_ARGS;
    }
}
static const struct sys_fd_ops caam_ops = {
    .ioctl = sys_caam_ioctl,
};
void platform_init_caam(uint level) {
    install_sys_fd_handler(SYSCALL_PLATFORM_FD_CAAM, &caam_ops);
}

LK_INIT_HOOK(caam_dev_init, platform_init_caam, LK_INIT_LEVEL_KERNEL - 1);
