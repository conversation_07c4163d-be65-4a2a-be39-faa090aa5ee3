/*
 * Copyright 2024 NXP
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#include <debug.h>
#include <err.h>
#include <kernel/vm.h>
#include <lib/rctee/sys_fd.h>
#include <lib/rctee/rctee_app.h>
#include <lk/init.h>
#include <mm.h>
#include <stdio.h>
#include <string.h>
#include <trace.h>
#include <platform/imx_csu.h>
#include <platform/imx_dpu.h>
#include <imx-regs.h>
#include <reg.h>
#include <lib/sm/smcall.h>
#include <lib/sm.h>
#include <platform/imx95-dpu-regs.h>

#define DRIVER_FD SYSCALL_PLATFORM_FD_DPU
#define CHECK_FD(x) \
                do { if(x!=DRIVER_FD) return ERR_BAD_HANDLE; } while (0)

#define FRAMECOMPLETE0 19

#define LOCAL_TRACE     5

#define SMC_ENTITY_IMX_LINUX_OPT 54
#define SMC_IMX_ECHO SMC_FASTCALL_NR(SMC_ENTITY_IMX_LINUX_OPT, 0)
#define SMC_IMX_DPU_REG_SET SMC_FASTCALL_NR(SMC_ENTITY_IMX_LINUX_OPT, 1)
#define SMC_IMX_DPU_REG_GET SMC_FASTCALL_NR(SMC_ENTITY_IMX_LINUX_OPT, 2)
#define SMC_IMX_DPU_REG_WRITE SMC_FASTCALL_NR(SMC_ENTITY_IMX_LINUX_OPT, 3)
#define SMC_IMX_DPU_REG_READ SMC_FASTCALL_NR(SMC_ENTITY_IMX_LINUX_OPT, 4)

#define  R_BITS(n)			(((n) & 0xf) << 24)
#define  G_BITS(n)			(((n) & 0xf) << 16)
#define  B_BITS(n)			(((n) & 0xf) << 8)
#define  A_BITS(n)			((n) & 0xf)
#define  Y_BITS(n)			R_BITS(n)
#define  Y_BITS_MASK			0xf000000
#define  U_BITS(n)			G_BITS(n)
#define  U_BITS_MASK			0xf0000
#define  V_BITS(n)			B_BITS(n)
#define  V_BITS_MASK			0xf00

#define  R_SHIFT(n)			(((n) & 0x1f) << 24)
#define  G_SHIFT(n)			(((n) & 0x1f) << 16)
#define  B_SHIFT(n)			(((n) & 0x1f) << 8)
#define  A_SHIFT(n)			((n) & 0x1f)
#define  Y_SHIFT(n)			R_SHIFT(n)
#define  Y_SHIFT_MASK			0x1f000000
#define  U_SHIFT(n)			G_SHIFT(n)
#define  U_SHIFT_MASK			0x1f0000
#define  V_SHIFT(n)			B_SHIFT(n)
#define  V_SHIFT_MASK			0x1f00

#define DRM_FORMAT_ARGB8888_BITS      (R_BITS(8)|G_BITS(8)|B_BITS(8)|A_BITS(8))
#define DRM_FORMAT_ARGB8888_SHIFTS    (R_SHIFT(16)| G_SHIFT(8)| B_SHIFT(0)| A_SHIFT(24))
#define DRM_FORMAT_XRGB8888_BITS      (R_BITS(8)| G_BITS(8)| B_BITS(8)| A_BITS(0))
#define DRM_FORMAT_XRGB8888_SHIFTS    (R_SHIFT(16)| G_SHIFT(8)| B_SHIFT(0)| A_SHIFT(0))
#define DRM_FORMAT_ABGR8888_BITS      (R_BITS(8)| G_BITS(8)| B_BITS(8)| A_BITS(8))
#define DRM_FORMAT_ABGR8888_SHIFTS    (R_SHIFT(0)| G_SHIFT(8)| B_SHIFT(16)| A_SHIFT(24))
#define DRM_FORMAT_XBGR8888_BITS      (R_BITS(8)| G_BITS(8)| B_BITS(8)| A_BITS(8))
#define DRM_FORMAT_XBGR8888_SHIFTS    (R_SHIFT(0)| G_SHIFT(8)| B_SHIFT(16)| A_SHIFT(0))
#define DRM_FORMAT_RGBA8888_BITS      (R_BITS(8)| G_BITS(8)| B_BITS(8)| A_BITS(8))
#define DRM_FORMAT_RGBA8888_SHIFTS    (R_SHIFT(24)| G_SHIFT(16)| B_SHIFT(8)| A_SHIFT(0))
#define DRM_FORMAT_RGBX8888_BITS      (R_BITS(8)| G_BITS(8)| B_BITS(8)| A_BITS(0))
#define DRM_FORMAT_RGBX8888_SHIFTS    (R_SHIFT(24)| G_SHIFT(16)| B_SHIFT(8)| A_SHIFT(0))
#define DRM_FORMAT_BGRA8888_BITS      (R_BITS(8)| G_BITS(8)| B_BITS(8)| A_BITS(8))
#define DRM_FORMAT_BGRA8888_SHIFTS    (R_SHIFT(8)| G_SHIFT(16)| B_SHIFT(24)| A_SHIFT(0))
#define DRM_FORMAT_BGRX8888_BITS      (R_BITS(8)| G_BITS(8)| B_BITS(8)| A_BITS(0))
#define DRM_FORMAT_BGRX8888_SHIFTS    (R_SHIFT(8)| G_SHIFT(16)| B_SHIFT(24)| A_SHIFT(0))
#define DRM_FORMAT_RGB565_BITS        (R_BITS(5)| G_BITS(6)| B_BITS(5)| A_BITS(0))
#define DRM_FORMAT_RGB565_SHIFTS      (R_SHIFT(11)| G_SHIFT(5)| B_SHIFT(0)| A_SHIFT(0))
#define DRM_FORMAT_YUYV_BITS          (Y_BITS(8)| U_BITS(8)| V_BITS(8)| A_BITS(0))
#define DRM_FORMAT_YUYV_SHIFTS        (Y_SHIFT(0)| U_SHIFT(8)| V_SHIFT(8)| A_SHIFT(0))
#define DRM_FORMAT_UYVY_BITS          (Y_BITS(8)| U_BITS(8)| V_BITS(8)| A_BITS(0))
#define DRM_FORMAT_UYVY_SHIFTS        (Y_SHIFT(8)| U_SHIFT(0)| V_SHIFT(0)| A_SHIFT(0))
#define DRM_FORMAT_NV12_BITS          (Y_BITS(8)| U_BITS(8)| V_BITS(8)| A_BITS(0))
#define DRM_FORMAT_NV12_SHIFTS        (Y_SHIFT(0)| U_SHIFT(0)| V_SHIFT(8)| A_SHIFT(0))
#define DRM_FORMAT_NV21_BITS          (Y_BITS(8)| U_BITS(8)| V_BITS(8)| A_BITS(0))
#define DRM_FORMAT_NV21_SHIFTS        (Y_SHIFT(0)| U_SHIFT(8)| V_SHIFT(0)| A_SHIFT(0))

#define LINEWIDTH(v)                  (((v) & 0x3fff) + 1)
#define LINECOUNT(v)                  ((((v) >> 16) & 0x3fff) + 1)

#define ALIGN_PIXEL_2(x) ((x + 1) & ~1)
#define ALIGN_PIXEL_4(x) ((x + 3) & ~3)
#define ALIGN_PIXEL_8(x) ((x + 7) & ~7)
#define ALIGN_PIXEL_16(x) ((x + 15) & ~15)
#define ALIGN_PIXEL_32(x) ((x + 31) & ~31)
#define ALIGN_PIXEL_64(x) ((x + 63) & ~63)
#define ALIGN_PIXEL_256(x) ((x + 255) & ~255)

#define EXTDST0_SYNC (0x111000 + 0x14)
#define EXTDST1_SYNC (0x121000 + 0x14)
#define EXTDST2_SYNC (0x151000 + 0x14)
#define EXTDST3_SYNC (0x161000 + 0x14)

static bool tee_ctrl_dpu= false;
static uint32_t last_tee_fb_addr = 0x0;
static uint8_t* nv12_framebuffer_base = NULL;
static uint8_t* yuyv_framebuffer_base = NULL;
static uint8_t* uyuv_framebuffer_base = NULL;
static uint8_t* rgba_framebuffer_base = NULL;
static uint8_t* rgb5_framebuffer_base = NULL;
static u32 dummy_framebuffer_width = 1920;
static u32 dummy_framebuffer_height = 1080;

static void setup_dummy_framebuffer_default() {

    size_t nv12_size = ALIGN_PIXEL_16(dummy_framebuffer_width) * ALIGN_PIXEL_4(dummy_framebuffer_height) * 3 / 2;
    size_t yuyv_size = ALIGN_PIXEL_16(dummy_framebuffer_width) * ALIGN_PIXEL_4(dummy_framebuffer_height) * 2;
    size_t rgba_size = (dummy_framebuffer_width * dummy_framebuffer_width * 4);
    size_t nv12_framebuffer_size = round_up(nv12_size, PAGE_SIZE);
    size_t yuyv_framebuffer_size = round_up(yuyv_size, PAGE_SIZE);
    size_t uyuv_framebuffer_size = round_up(yuyv_size, PAGE_SIZE);
    size_t rgba_framebuffer_size = round_up(rgba_size, PAGE_SIZE);
    size_t rgb5_framebuffer_size = round_up(yuyv_size, PAGE_SIZE);

    /* prepare rgb framebuffer */
    rgba_framebuffer_base = (uint8_t*)SECURE_MEMORY_VIRT;
    memset(rgba_framebuffer_base, 0, rgba_framebuffer_size);
    /* prepare rgb5 framebuffer */
    rgb5_framebuffer_base = (uint8_t*)rgba_framebuffer_base + rgba_framebuffer_size;
    memset(rgba_framebuffer_base, 0, rgb5_framebuffer_size);
    /* prepare yuyv framebuffer */
    yuyv_framebuffer_base = (uint8_t*)rgb5_framebuffer_base + rgb5_framebuffer_size;
    memset(yuyv_framebuffer_base, 0, yuyv_framebuffer_size);
    for (size_t i = 0; i < yuyv_framebuffer_size / 2; ++i) {
        *(yuyv_framebuffer_base + 2 * i) = 0xf;
    }
    /* prepare uyuv framebuffer */
    uyuv_framebuffer_base = (uint8_t*)yuyv_framebuffer_base + yuyv_framebuffer_size;
    memset(uyuv_framebuffer_base, 0, uyuv_framebuffer_size);
    for (size_t i = 0; i < uyuv_framebuffer_size / 2; ++i) {
        *(uyuv_framebuffer_base + 2 * i + 1) = 0xf;
    }
    /* prepare nv12 framebuffer */
    nv12_framebuffer_base = (uint8_t*)uyuv_framebuffer_base + uyuv_framebuffer_size;
    memset(nv12_framebuffer_base, 0xf, nv12_framebuffer_size);
    memset(nv12_framebuffer_base + nv12_framebuffer_size, 0x80, nv12_framebuffer_size / 4);
    memset(nv12_framebuffer_base + nv12_framebuffer_size * 5 / 4, 0x80, nv12_framebuffer_size / 4);
}

static u32 find_framebuffer(u32 target, u32 reg_offset1) {
    u64 framebuffer_virt;
    u32 framebuffer_paddr;

    u32 bits = *REG32((uint8_t*)DPU_BASE_VIRT + target + 0x18 + reg_offset1);
    u32 shift_bits = *REG32((uint8_t*)DPU_BASE_VIRT + target + 0x1c + reg_offset1);

    if (bits == DRM_FORMAT_ARGB8888_BITS && shift_bits == DRM_FORMAT_ARGB8888_SHIFTS)
        framebuffer_virt = (u64)rgba_framebuffer_base;
    else if (bits == DRM_FORMAT_XRGB8888_BITS && shift_bits == DRM_FORMAT_XRGB8888_SHIFTS)
        framebuffer_virt = (u64)rgba_framebuffer_base;
    else if (bits == DRM_FORMAT_ABGR8888_BITS && shift_bits == DRM_FORMAT_ABGR8888_SHIFTS)
        framebuffer_virt = (u64)rgba_framebuffer_base;
    else if (bits == DRM_FORMAT_XBGR8888_BITS && shift_bits == DRM_FORMAT_XBGR8888_SHIFTS)
        framebuffer_virt = (u64)rgba_framebuffer_base;
    else if (bits == DRM_FORMAT_RGBA8888_BITS && shift_bits == DRM_FORMAT_RGBA8888_SHIFTS)
        framebuffer_virt = (u64)rgba_framebuffer_base;
    else if (bits == DRM_FORMAT_RGBX8888_BITS && shift_bits == DRM_FORMAT_RGBX8888_SHIFTS)
        framebuffer_virt = (u64)rgba_framebuffer_base;
    else if (bits == DRM_FORMAT_BGRA8888_BITS && shift_bits == DRM_FORMAT_BGRA8888_SHIFTS)
        framebuffer_virt = (u64)rgba_framebuffer_base;
    else if (bits == DRM_FORMAT_BGRX8888_BITS && shift_bits == DRM_FORMAT_BGRX8888_SHIFTS)
        framebuffer_virt = (u64)rgba_framebuffer_base;
    else if (bits == DRM_FORMAT_RGB565_BITS && shift_bits == DRM_FORMAT_RGB565_SHIFTS)
        framebuffer_virt = (u64)rgb5_framebuffer_base;
    else if (bits == DRM_FORMAT_YUYV_BITS && shift_bits == DRM_FORMAT_YUYV_SHIFTS)
        framebuffer_virt = (u64)yuyv_framebuffer_base;
    else if (bits == DRM_FORMAT_UYVY_BITS && shift_bits == DRM_FORMAT_UYVY_SHIFTS)
        framebuffer_virt = (u64)uyuv_framebuffer_base;
    else if (bits == (DRM_FORMAT_NV12_BITS & ~(U_BITS_MASK | V_BITS_MASK)) && shift_bits == (DRM_FORMAT_NV12_SHIFTS & ~(U_SHIFT_MASK | V_SHIFT_MASK)))
        framebuffer_virt = (u64)nv12_framebuffer_base;
    else if (bits == (DRM_FORMAT_NV21_BITS & ~(U_BITS_MASK | V_BITS_MASK)) && shift_bits == (DRM_FORMAT_NV21_SHIFTS & ~(U_SHIFT_MASK | V_SHIFT_MASK)))
        framebuffer_virt = (u64)nv12_framebuffer_base;
    else {
        LTRACEF("it is not support this format\n");
        framebuffer_virt = 0x0;
    }
    framebuffer_paddr = (framebuffer_virt & 0xffffffff);
    return framebuffer_paddr;
}

static int32_t imx_secure_disp(uint32_t cmd, user_addr_t user_ptr) {
    struct csu_cfg_secure_disp_msg *msg = NULL;
    int ret = -1;
    msg = memalign(64, sizeof(struct csu_cfg_secure_disp_msg));
    if (!msg)
        panic("out of memory allocating dispmsg\n");

    ret = copy_from_user(msg, user_ptr, sizeof(struct csu_cfg_secure_disp_msg));
    if (unlikely(ret != 0)) {
        LTRACEF("%s: failed to copy data from user!\n", __func__ );
        free(msg);
        return -1;
    }
    if (msg->enable) {
        tee_ctrl_dpu = true;
        last_tee_fb_addr = msg->paddr;
    } else {
        tee_ctrl_dpu = false;
    }

    if (msg)
        free(msg);
    return 0;
}

static void setup_dummy_framebuffer(u32 target, u32 reg_offset1, u64 framebuffer_paddr) {
    u64 framebuffer_vaddr = (0xffffffff00000000 | framebuffer_paddr);
    /* nothing to do for rgba and yuyv*/
    if ((framebuffer_vaddr == (u64)rgba_framebuffer_base) || (framebuffer_vaddr == (u64)rgb5_framebuffer_base)
        || (framebuffer_vaddr == (u64)yuyv_framebuffer_base) || (framebuffer_vaddr == (u64)uyuv_framebuffer_base))
         return;
    u32 val = *REG32((uint8_t*)DPU_BASE_VIRT + target + 0x14 + reg_offset1);
    dummy_framebuffer_width = ALIGN_PIXEL_16(LINEWIDTH(val));
    dummy_framebuffer_height = ALIGN_PIXEL_4(LINECOUNT(val));

    /*
     * Becasue If there are multiple nv12 planes,
     * if the size of the framebuffer is page-aligned,
     * it will affect the color of the image.
     * */
    u32 real_dimension = dummy_framebuffer_width * dummy_framebuffer_height;
    if ((dummy_framebuffer_width > 1920) || (dummy_framebuffer_height > 1080)) {
        LTRACEF("the size of secure ui is not support\n");
        return;
    }

    if (framebuffer_vaddr == (u64)nv12_framebuffer_base) {
        memset(nv12_framebuffer_base, 0xf, real_dimension);
        memset(nv12_framebuffer_base + real_dimension, 0x80, real_dimension / 4);
        memset(nv12_framebuffer_base + real_dimension * 5 / 4, 0x80, real_dimension / 4);
    } else {
        LTRACEF("framebuffer format is not correct\n");
    }

}

static long imx_linux_dpu_reg_get(struct smc32_args* args) {
    u32 target = args->params[0];
    u32 offset = args->params[1];
    switch (target) {
        case DPU_CONSTFRAME0:
        case DPU_CONSTFRAME1:
        case DPU_CONSTFRAME2:
        case DPU_CONSTFRAME3:
        case DPU_AUX_CONSTFRAME0:
        case DPU_AUX_CONSTFRAME1:
        case DPU_AUX_CONSTFRAME2:
        case DPU_AUX_CONSTFRAME3:
        case DPU_DOMAINBLEND0:
        case DPU_DOMAINBLEND1:
        case DPU_DITHER0:
        case DPU_DITHER1:
        case DPU_AUX_DITHER0:
        case DPU_AUX_DITHER1:
        case DPU_FETCHECO0:
        case DPU_FETCHECO1:
        case DPU_FETCHECO2:
        case DPU_FETCHECO3:
        case DPU_AUX_FETCHECO0:
        case DPU_AUX_FETCHECO1:
        case DPU_AUX_FETCHECO2:
        case DPU_AUX_FETCHECO3:
        case DPU_FETCHYUV0:
        case DPU_FETCHYUV1:
        case DPU_FETCHYUV2:
        case DPU_FETCHYUV3:
        case DPU_AUX_FETCHYUV0:
        case DPU_AUX_FETCHYUV1:
        case DPU_AUX_FETCHYUV2:
        case DPU_AUX_FETCHYUV3:
        case DPU_EXTDST0:
        case DPU_EXTDST1:
        case DPU_EXTDST2:
        case DPU_EXTDST3:
        case DPU_AUX_EXTDST0:
        case DPU_AUX_EXTDST1:
        case DPU_AUX_EXTDST2:
        case DPU_AUX_EXTDST3:
        case DPU_FRAMEGEN0:
        case DPU_FRAMEGEN1:
        case DPU_HSCALER0:
        case DPU_HSCALER1:
        case DPU_AUX_HSCALER0:
        case DPU_AUX_HSCALER1:
        case DPU_FETCHLAYER0:
        case DPU_FETCHLAYER1:
        case DPU_AUX_FETCHLAYER0:
        case DPU_AUX_FETCHLAYER1:
        case DPU_BLIT:
        case DPU_LAYERBLEND0:
        case DPU_LAYERBLEND1:
        case DPU_LAYERBLEND2:
        case DPU_LAYERBLEND3:
        case DPU_LAYERBLEND4:
        case DPU_LAYERBLEND5:
        case DPU_AUX_LAYERBLEND0:
        case DPU_AUX_LAYERBLEND1:
        case DPU_AUX_LAYERBLEND2:
        case DPU_AUX_LAYERBLEND3:
        case DPU_AUX_LAYERBLEND4:
        case DPU_AUX_LAYERBLEND5:
            if (((target + offset) >= 0) && ((target + offset) < DPU_REGS_SIZE))
                return *REG32((uint8_t*)DPU_BASE_VIRT + target + offset);
            else {
                LTRACEF("Exceeded the address range of the DPU register\n");
                return -1;
            }
        default:
            LTRACEF("imx_linux_dpu_reg_get wrong target for 0x%x\n", target + offset);
            return -1;
    }
}

static long imx_linux_dpu_reg_set(struct smc32_args* args) {
    u32 target = args->params[0];
    u32 offset = args->params[1];
    u32 val = args->params[2];
    switch (target) {
        case DPU_CONSTFRAME0:
        case DPU_CONSTFRAME1:
        case DPU_CONSTFRAME2:
        case DPU_CONSTFRAME3:
        case DPU_AUX_CONSTFRAME0:
        case DPU_AUX_CONSTFRAME1:
        case DPU_AUX_CONSTFRAME2:
        case DPU_AUX_CONSTFRAME3:
        case DPU_DOMAINBLEND0:
        case DPU_DOMAINBLEND1:
        case DPU_DITHER0:
        case DPU_DITHER1:
        case DPU_AUX_DITHER0:
        case DPU_AUX_DITHER1:
        case DPU_AUX_FETCHECO0:
        case DPU_AUX_FETCHECO1:
        case DPU_AUX_FETCHECO2:
        case DPU_AUX_FETCHECO3:
        case DPU_BLIT:
        case DPU_EXTDST0:
        case DPU_EXTDST1:
        case DPU_EXTDST2:
        case DPU_EXTDST3:
        case DPU_AUX_EXTDST0:
        case DPU_AUX_EXTDST1:
        case DPU_AUX_EXTDST2:
        case DPU_AUX_EXTDST3:
        case DPU_FRAMEGEN0:
        case DPU_FRAMEGEN1:
        case DPU_HSCALER0:
        case DPU_HSCALER1:
        case DPU_AUX_HSCALER0:
        case DPU_AUX_HSCALER1:
        case DPU_AUX_FETCHLAYER0:
        case DPU_AUX_FETCHLAYER1:
        case DPU_AUX_FETCHYUV0:
        case DPU_AUX_FETCHYUV1:
        case DPU_AUX_FETCHYUV2:
        case DPU_AUX_FETCHYUV3:
        case DPU_LAYERBLEND0:
        case DPU_LAYERBLEND1:
        case DPU_LAYERBLEND2:
        case DPU_LAYERBLEND3:
        case DPU_LAYERBLEND4:
        case DPU_LAYERBLEND5:
        case DPU_AUX_LAYERBLEND0:
        case DPU_AUX_LAYERBLEND1:
        case DPU_AUX_LAYERBLEND2:
        case DPU_AUX_LAYERBLEND3:
        case DPU_AUX_LAYERBLEND4:
        case DPU_AUX_LAYERBLEND5:
        /* to do check */
            if (((target + offset) >= 0) && ((target + offset) < DPU_REGS_SIZE)) {
                writel(val, (uint8_t*)DPU_BASE_VIRT + target + offset);
                return 0;
            }
            else {
                LTRACEF("writing exceeded the address range of the DPU register\n");
                return -1;
            }
        case DPU_FETCHLAYER0:
        case DPU_FETCHLAYER1:
            if (target == DPU_FETCHLAYER0) {
                /* fetchlayer0 resource is assigned to primary plane by default*/
                if (offset == DPU_FRAMEBUFFE) {
                    if (tee_ctrl_dpu) {
                        writel(last_tee_fb_addr, (uint8_t*)DPU_BASE_VIRT + target + offset);
                    } else
                        writel(val, (uint8_t*)DPU_BASE_VIRT + target + offset);
                } else if (offset == DPU_FRAMEBUFFEMB) {
                    if (tee_ctrl_dpu)
                        writel(0, (uint8_t*)DPU_BASE_VIRT + target + offset);
                    else
                        writel(val, (uint8_t*)DPU_BASE_VIRT + target + offset);
                } else {
                    if (((target + offset) >= 0) && ((target + offset) < DPU_REGS_SIZE))
                        writel(val, (uint8_t*)DPU_BASE_VIRT + target + offset);
                    else {
                        LTRACEF("writing exceeded the address range of the DPU register\n");
                        return -1;
                    }
                }
            } else {
                /* fetchlayer1 is assigned to overlay plane by default*/
                if (offset == DPU_FRAMEBUFFE) {
                    if (tee_ctrl_dpu) {
                        val = find_framebuffer(target, 0x18);
                        setup_dummy_framebuffer(target, 0x18, val);
                        writel(val, (uint8_t*)DPU_BASE_VIRT + target + offset);
                    } else {
                        writel(val, (uint8_t*)DPU_BASE_VIRT + target + offset);
                    }
                } else if (offset == DPU_FRAMEBUFFEMB) {
                    if (tee_ctrl_dpu)
                        writel(0, (uint8_t*)DPU_BASE_VIRT + target + offset);
                    else
                        writel(val, (uint8_t*)DPU_BASE_VIRT + target + offset);
                } else {
                    if (((target + offset) >= 0) && ((target + offset) < DPU_REGS_SIZE))
                        writel(val, (uint8_t*)DPU_BASE_VIRT + target + offset);
                    else {
                        LTRACEF("writing exceeded the address range of the DPU register\n");
                        return -1;
                    }
                }
            }
            return 0;
        case DPU_FETCHYUV0:
        case DPU_FETCHYUV1:
        case DPU_FETCHYUV2:
        case DPU_FETCHYUV3:
            /* fetchyuv0-3 is assigned to overlay plane by default on android*/
            if (offset == 0x28){
                if (tee_ctrl_dpu) {
                    val = find_framebuffer(target, 0x28);
                    setup_dummy_framebuffer(target, 0x28, val);
                    writel(val, (uint8_t*)DPU_BASE_VIRT + target + offset);
                } else {
                    writel(val, (uint8_t*)DPU_BASE_VIRT + target + offset);
                }
            } else if (offset == 0x2c){
                if (tee_ctrl_dpu)
                    writel(0, (uint8_t*)DPU_BASE_VIRT + target + offset);
                else
                    writel(val, (uint8_t*)DPU_BASE_VIRT + target + offset);
            } else {
                if (((target + offset) >= 0) && ((target + offset) < DPU_REGS_SIZE))
                    writel(val, (uint8_t*)DPU_BASE_VIRT + target + offset);
                else {
                    LTRACEF("writing exceeded the address range of the DPU register\n");
                    return -1;
                }
            }
            return 0;
        case DPU_FETCHECO0:
        case DPU_FETCHECO1:
        case DPU_FETCHECO2:
        case DPU_FETCHECO3:
            /* fetcheco0-3 is only for nv12 format*/
            if (offset == 0x10){
                if (tee_ctrl_dpu) {
                    writel(((u64)nv12_framebuffer_base & 0xffffffff) + dummy_framebuffer_width * dummy_framebuffer_height,
                    (uint8_t*)DPU_BASE_VIRT + target + offset);
                } else {
                    writel(val, (uint8_t*)DPU_BASE_VIRT + target + offset);
                }
            } else if (offset == 0x14){
                if (tee_ctrl_dpu)
                    writel(0, (uint8_t*)DPU_BASE_VIRT + target + offset);
                else
                    writel(val, (uint8_t*)DPU_BASE_VIRT + target + offset);
            } else {
                if (((target + offset) >= 0) && ((target + offset) < DPU_REGS_SIZE))
                    writel(val, (uint8_t*)DPU_BASE_VIRT + target + offset);
                else {
                    LTRACEF("writing exceeded the address range of the DPU register\n");
                    return -1;
                }
            }
            return 0;
        default:
            LTRACEF("imx_linux_dpu_reg wrong target for 0x%x\n", target + offset);
            return -1;
    }
}

static long imx_uboot_dpu_reg_write(struct smc32_args* args) {
    u32 addr = args->params[0];
    u32 var = args->params[1];

    /* to do check */
    if (((addr) >= DPU_REGS_PHY) && ((addr - DPU_REGS_PHY) < DPU_REGS_SIZE))
        if (tee_ctrl_dpu) {
            LTRACEF("illegal operation\n");
            return -1;
        } else
            writel(var, DPU_PHY2VIRT((uint8_t *)(intptr_t)addr));
    else {
        LTRACEF("writing exceeded the address range of the DPU register\n");
        return -1;
    }
    return 0;
}

static long imx_uboot_dpu_reg_read(struct smc32_args* args) {
    u32 addr = args->params[0];

    /* to do check */
    if (((addr) >= DPU_REGS_PHY) && ((addr - DPU_REGS_PHY) < DPU_REGS_SIZE))
        if (tee_ctrl_dpu) {
                LTRACEF("illegal operation\n");
                return -1;
        } else
            return *REG32(DPU_PHY2VIRT((uint8_t *)(intptr_t)addr));
    else {
        LTRACEF("reading exceeded the address range of the DPU register\n");
        return -1;
    }
}

static long imx_linux_fastcall(struct smc32_args* args) {
    switch (args->smc_nr) {
        case SMC_IMX_ECHO:
            return 0;
        case SMC_IMX_DPU_REG_SET:
            return imx_linux_dpu_reg_set(args);
        case SMC_IMX_DPU_REG_GET:
            return imx_linux_dpu_reg_get(args);
        case SMC_IMX_DPU_REG_WRITE:
            return imx_uboot_dpu_reg_write(args);
        case SMC_IMX_DPU_REG_READ:
            return imx_uboot_dpu_reg_read(args);
        default:
            return 0;
    }
}

static int32_t sys_dpu_ioctl(uint32_t fd, uint32_t cmd, user_addr_t user_ptr) {
    CHECK_FD(fd);
    switch (cmd) {
        case DPU_IOCMD_SECURE_DISP:
            return imx_secure_disp(cmd, user_ptr);
        case SETUP_DUMMY_FRAMEBUFFER:
            setup_dummy_framebuffer_default();
            return 0;
        default:
            return 0;
    }
    return 0;
}

static struct smc32_entity imx_linux_entity = {
    .fastcall_handler = imx_linux_fastcall,
};
static const struct sys_fd_ops dpu_ops = {
    .ioctl = sys_dpu_ioctl,
};


void imx_linux_smcall_init(uint level) {
    sm_register_entity(SMC_ENTITY_IMX_LINUX_OPT, &imx_linux_entity);
}

void platform_init_dpu(uint level) {
    install_sys_fd_handler(SYSCALL_PLATFORM_FD_DPU, &dpu_ops);
}

LK_INIT_HOOK(sys_dpu_ioctl, platform_init_dpu, LK_INIT_LEVEL_PLATFORM + 1);
LK_INIT_HOOK(imx_linux_driver, imx_linux_smcall_init, LK_INIT_LEVEL_PLATFORM);
