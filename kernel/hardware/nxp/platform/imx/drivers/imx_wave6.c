/*
 * Copyright 2024 NXP
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#include <debug.h>
#include <err.h>
#include <kernel/vm.h>
#include <lib/rctee/sys_fd.h>
#include <lib/rctee/rctee_app.h>
#include <lk/init.h>
#include <mm.h>
#include <stdio.h>
#include <string.h>
#include <trace.h>
#include <platform/imx_csu.h>
#include <platform/imx_dpu.h>
#include <imx-regs.h>
#include <reg.h>
#include <lib/sm/smcall.h>
#include <lib/sm.h>
#include <platform/imx-wave6-regs.h>
#include <platform/imx_wave6.h>
#include <kernel/mutex.h>

#define LOCAL_TRACE     5

#define DRIVER_FD SYSCALL_PLATFORM_FD_WAVE6
#define CHECK_FD(x) \
                do { if(x!=DRIVER_FD) return ERR_BAD_HANDLE; } while (0)

#define SMC_ENTITY_IMX_WAVE_LINUX_OPT 55
#define SMC_IMX_ECHO SMC_FASTCALL_NR(SMC_ENTITY_IMX_WAVE_LINUX_OPT, 0)
#define SMC_IMX_VPU_REG SMC_FASTCALL_NR(SMC_ENTITY_IMX_WAVE_LINUX_OPT, 1)
#define SMC_IMX_VCPU_REG SMC_FASTCALL_NR(SMC_ENTITY_IMX_WAVE_LINUX_OPT, 2)

#define OPT_WRITE 0x1
#define OPT_READ  0x2
#define SECURE_HEAP_BASE 0xA0000000
#define SECURE_HEAP_SIZE 0x1EF00000

/* store reg addr */
static u32 secure_inputbuffer[2];
static u32 secure_outputbuffer[3];
static u32 secure_framebuffer[9];
static bool secure_mode = false;

enum check_buffer_type {
    CHECK_INPUT_BUFFER = 0,
    CHECK_OUTPUT_BUFFER,
    CHECK_FRAMEBUFFER
};

static u64 check(u32 reg) {
    u32 addr = *REG32((uint8_t*)VPU_BASE_VIRT + reg);
    if (secure_mode && (((addr >= SECURE_HEAP_BASE) && (addr < (SECURE_HEAP_BASE + SECURE_HEAP_SIZE)))
        || (addr == 0x0) || (addr == 0xffffffff))) {
        return 1;
    } else {
        return 0;
    }
}

static bool check_secure_buffer(enum check_buffer_type type) {
    u64 secure = 0;
    u64 check_result = 0;
    bool ret = true;
    /* if it is not in secure mode, there is no need to check buffer*/
    if (!secure_mode)
        return true;
    if (type == CHECK_INPUT_BUFFER) {
        check_result = 0b11;
        for (int i = 0; i < 2; i++)
            secure |= (check(secure_inputbuffer[i]) << i);
        if (secure == check_result)
            ret = true;
        else
            ret = false;
    } else if (type == CHECK_OUTPUT_BUFFER) {
        check_result = 0b111;
        for (int i = 0; i < 3; i++)
            secure |= (check(secure_outputbuffer[i]) << i);
        if (secure == check_result)
            ret = true;
        else
            ret = false;
    } else if (type == CHECK_FRAMEBUFFER) {
         u32 reg_val = *REG32((uint8_t*)VPU_BASE_VIRT + 0x308);
         int fbc_start_no = (reg_val >> 24) & 0xFF;
         int fbc_end_no = (reg_val >> 16) & 0xFF;
         int mv_start_no = (reg_val >> 5)  & 0xFFFF;
         int mv_end_no = (reg_val >> 0)  & 0x1F;
         u8 fb_num = fbc_end_no - fbc_start_no + 1;
         u8 mv_num = mv_end_no - mv_start_no + 1;
         check_result = ((1ULL) << (fb_num * 6 + mv_num + 2)) - 1;
         for (int i = 0; i < fb_num; i++) {
             secure |= (check(secure_framebuffer[0] + (i * 24)) << (i * 6 ));
             secure |= (check(secure_framebuffer[1] + (i * 24)) << (i * 6 + 1));;
             secure |= (check(secure_framebuffer[2] + (i * 8)) << (i * 6 + 2));
             secure |= (check(secure_framebuffer[3] + (i * 24)) << (i * 6 + 3));
             secure |= (check(secure_framebuffer[4] + (i * 24)) << (i * 6 + 4));
             secure |= (check(secure_framebuffer[5] + (i * 8)) << (i * 6 + 5));
         }
         for (int j = 0; j < mv_num; j++) {
             secure |= (check(secure_framebuffer[6] + (j * 24)) << (fb_num * 6 + j));
         }
         secure |= (check(secure_framebuffer[7]) << (fb_num * 6 + mv_num));
         secure |= (check(secure_framebuffer[8]) << (fb_num * 6 + mv_num + 1));
         if (secure == check_result)
            ret = true;
         else
            ret = false;
    }
    return ret;
}

static void init_secure_buffer() {
    secure_inputbuffer[0] = W6_CMD_DEC_PIC_BS_RD_PTR;
    secure_inputbuffer[1] = W6_CMD_DEC_PIC_BS_WR_PTR;

    secure_outputbuffer[0] = W6_CMD_DEC_SET_DISP_Y_BASE;
    secure_outputbuffer[1] = W6_CMD_DEC_SET_DISP_CB_BASE;
    secure_outputbuffer[2] = W6_CMD_DEC_SET_DISP_CR_BASE;

    secure_framebuffer[0] = W6_CMD_DEC_SET_DISP_CB_BASE;
    secure_framebuffer[1] = W6_CMD_DEC_SET_DISP_CR_BASE;
    secure_framebuffer[2] = W6_CMD_DEC_SET_FB_FBC_CR0;
    secure_framebuffer[3] = W6_CMD_DEC_SET_FB_FBC_Y_OFFSET0;
    secure_framebuffer[4] = W6_CMD_DEC_SET_FB_FBC_C_OFFSET0;
    secure_framebuffer[5] = W6_CMD_DEC_SET_FB_FBC_CR_OFFSET0;
    secure_framebuffer[6] = W6_CMD_DEC_SET_FB_MV_COL0;
    secure_framebuffer[7] = W6_CMD_DEC_SET_FB_DEFAULT_CDF;
    secure_framebuffer[8] = W6_CMD_DEC_SET_FB_MV_COL_PRE_ENT;
}

static int imx_linux_vpu_reg(struct smc32_args* args, u32 *ret_val) {
    u32 target = args->params[0];
    u32 op = args->params[1];
    u32 val = args->params[2];
    bool secure_check = true;
    /* check target range */
    if (target < 0 || target >= VPU_REGS_SIZE) {
        LTRACEF("imx_linux_vpu_reg wrong target for 0x%x\n",target);
        return -1;
    }
    if (op == OPT_WRITE) {
        /* DEC_PIC command */
        if (target ==  W6_COMMAND) {
            if (val == W6_INIT_SEQ || val == W6_DEC_PIC) {
                /* check secure inputbuffer */
                secure_check = check_secure_buffer(CHECK_INPUT_BUFFER);
                if (secure_check) {
                    writel(val, (uint8_t*)VPU_BASE_VIRT + target);
                } else {
                    LTRACEF("input secure check failed\n");
                    return -1;
                }
            } else if (val == W6_DEC_SET_DISP_BUF) {
                /* check secure outbuffer */
                secure_check = check_secure_buffer(CHECK_OUTPUT_BUFFER);
                if (secure_check) {
                    writel(val, (uint8_t*)VPU_BASE_VIRT + target);
                } else {
                    LTRACEF("output secure check failed\n");
                    return -1;
                }
            } else if (val == W6_SET_FB) {
                /* check secure framebuffer */
                secure_check = check_secure_buffer(CHECK_FRAMEBUFFER);
                if (secure_check) {
                    writel(val, (uint8_t*)VPU_BASE_VIRT + target);
                } else {
                    LTRACEF("framebuffer secure check failed\n");
                    return -1;
                }
            } else {
                writel(val, (uint8_t*)VPU_BASE_VIRT + target);
                secure_check = true;
            }
       } else {
           writel(val, (uint8_t*)VPU_BASE_VIRT + target);
           secure_check = true;
       }
    } else if (op == OPT_READ) {
        *ret_val = *REG32((uint8_t*)VPU_BASE_VIRT + target);
    } else {
        LTRACEF("imx_linux_vpu_reg wrong op for 0x%x\n",op);
        return -1;
    }
    return 0;
}

static int imx_linux_vcpu_reg(struct smc32_args* args, u32 *ret_val) {
    u32 target = args->params[0];
    u32 op = args->params[1];
    u32 val = args->params[2];
    /* check target range */
    if (target < 0 || target >= VCPU_REGS_SIZE) {
        LTRACEF("imx_linux_vpu_reg wrong target for 0x%x\n",target);
        return -1;
    }
    if (op == OPT_WRITE) {
        if (target == W6_VPU_REMAP_PADDR_GB) {
            if (val != VCPU_BOOTMEM_PHY) {
                LTRACEF("vpu boot memory is not secure 0x%x\n",val);
                return -1;
            }
        }
        writel(val, (uint8_t*)VCPU_BASE_VIRT + target);
    } else if (op == OPT_READ) {
        *ret_val = *REG32((uint8_t*)VCPU_BASE_VIRT + target);
    } else {
        LTRACEF("imx_linux_vpu_reg wrong op for 0x%x\n",op);
        return -1;
    }
    return 0;
}

static long imx_linux_wave6_fastcall(struct smc32_args* args) {
    u32 val = 0;
    int ret = 0;
    switch (args->smc_nr) {
        case SMC_IMX_ECHO:
            return 0;
        case SMC_IMX_VPU_REG:
            ret = imx_linux_vpu_reg(args, &val);
            if (ret >= 0)
                return val;
            else
                return ret;
        case SMC_IMX_VCPU_REG:
            ret = imx_linux_vcpu_reg(args, &val);
            if (ret >= 0)
                return val;
            else
                return ret;
        default:
            return 0;
    }
}

static int32_t imx_set_secure_mode(uint32_t cmd, user_addr_t user_ptr) {
    struct wave6_secure_mode_msg *msg = NULL;
    int ret = -1;
    msg = memalign(64, sizeof(struct wave6_secure_mode_msg));
    if (!msg) {
        panic("out of memory allocating wave6 msg\n");
        return -1;
    }

    ret = copy_from_user(msg, user_ptr, sizeof(struct wave6_secure_mode_msg));
    if (unlikely(ret != 0)) {
        LTRACEF("%s: failed to copy data from user!\n", __func__ );
        free(msg);
        return -1;
    }
    secure_mode = msg->secure_mode;
    if (msg) {
        free(msg);
    }

    LTRACEF("wave6 use secure mode = %d\n", secure_mode);
    return ret;
}

static int32_t sys_wave6_ioctl(uint32_t fd, uint32_t cmd, user_addr_t user_ptr) {
    CHECK_FD(fd);
    switch (cmd) {
        case WAVE6_SECURE_MODE:
            return imx_set_secure_mode(cmd, user_ptr);
        default:
            return 0;
    }
    return 0;
}

static struct smc32_entity imx_linux_wave6_entity = {
    .fastcall_handler = imx_linux_wave6_fastcall,
};

void imx_linux_wave6_smcall_init(uint level) {
    init_secure_buffer();
    sm_register_entity(SMC_ENTITY_IMX_WAVE_LINUX_OPT, &imx_linux_wave6_entity);
}

static const struct sys_fd_ops wave6_ops = {
    .ioctl = sys_wave6_ioctl,
};

void platform_init_wave6(uint level) {
    install_sys_fd_handler(SYSCALL_PLATFORM_FD_WAVE6, &wave6_ops);
}

LK_INIT_HOOK(wave6_ioctl, platform_init_wave6, LK_INIT_LEVEL_PLATFORM + 1);
LK_INIT_HOOK(wave6_driver, imx_linux_wave6_smcall_init, LK_INIT_LEVEL_PLATFORM);

