/*
 * Copyright (C) 2016 Freescale Semiconductor, Inc.
 * Copyright 2017 NXP
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * 1. Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 *
 * 2. Redistributions in binary form must reproduce the above copyright notice,
 * this list of conditions and the following disclaimer in the documentation
 * and/or other materials provided with the distribution.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
 * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
 * ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE
 * LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
 * CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
 * <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
 * INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
 * CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
 * ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 */

#ifndef _IMX_REGS_H_
#define _IMX_REGS_H_

#define MACH_IMX8Q

#define GIC_BASE_PHY  0x51A00000
#define GIC_BASE_VIRT 0xFFFFFFFF51A00000

#define CONFIG_LPUART0_BASE 0x5A060000
#define CONFIG_LPUART1_BASE 0x5A070000
#define CONFIG_LPUART2_BASE 0x5A080000
#define CONFIG_LPUART3_BASE 0x5A090000
#define CONFIG_LPUART4_BASE 0x5A0A0000

#define CONFIG_CONSOLE_TTY_BASE  CONFIG_LPUART0_BASE

#define CONFIG_CONSOLE_TTY_VIRT (0xFFFFFFFF00000000 + CONFIG_CONSOLE_TTY_BASE)

#define CAAM_PHY_BASE_ADDR 0x31400000
#define CAAM_BASE_ADDR  (0xFFFFFFFF00000000 + CAAM_PHY_BASE_ADDR)
#define CAAM_PHY_ARB_BASE_ADDR 0x31800000
#define CAAM_ARB_BASE_ADDR (0xFFFFFFFF00000000 + CAAM_PHY_ARB_BASE_ADDR)

#define CAAM_REG_SIZE 0x50000

#define CCM_BASE_ADDR 0x30380000
#define CCM_BASE_ADDR_VIRT (0x40000000 + CCM_BASE_ADDR)
#define CAAM_SEC_RAM_SIZE 0x10000

/* Registers for GIC */
#define MAX_INT 160
#define GICBASE(b) (GIC_BASE_VIRT)

#define GICC_SIZE (0x1000)
#define GICD_SIZE (0x100)

#define SOC_REGS_PHY 0x20000000
#define SOC_REGS_VIRT 0xFFFFFFFF20000000
#define SOC_REGS_SIZE 0x60000000

#define GICC_OFFSET (0x1000)
#define GICD_OFFSET (0x0000)
#define GICR_OFFSET (0x100000)

#define GICC_BASE_VIRT (GIC_BASE_VIRT + GICC_OFFSET)
#define GICD_BASE_VIRT (GIC_BASE_VIRT + GICD_OFFSET)

#define GIC_REG_SIZE 0x2000

/* Registers for TZASC */
#define TZ_BASE 0x32F80000
#define TZ_BASE_VIRT (0x40000000 + TZ_BASE)
#define TZ_REG_SIZE 0x4000
#define TZ_BYPASS_GPR_BASE 0x30340024
#define TZ_BYPASS_GPR_BASE_VIRT (0x40000000 + TZ_BYPASS_GPR_BASE)

#define SECURE_RAM_START_ADDR_PHY CAAM_PHY_ARB_BASE_ADDR
#define SECURE_RAM_START_ADDR CAAM_ARB_BASE_ADDR
#define SECURE_RAM_SIZE 0x4000

/* Base address and size of MU4 */
#define SC_IPC_BASE    0x5d1f0000
#define SC_IPC_SIZE    0x10000

#if IMX8QM == 1

#define MACH_IMX8QM
/* secure heap address and size */
#define SECURE_HEAP_BASE  0xE0000000
#define SECURE_HEAP_SIZE  0x10000000

/* vpu firmware boot address and size */
#define VPU_FIRMWARE_BASE 0x96000000
#define VPU_FIRMWARE_SIZE 0x02000000
#define VPU_FIRMWARE_VIRT 0xFFFFFFFF96000000

#define VPU_REGS_BASE      (0xFFFFFFFF2C000000)
#define VPU_CORE_REGS_BASE (0xFFFFFFFF2D080000)

#else
#define MACH_IMX8QXP
#endif

#endif
