/*
 * Copyright (C) 2016 Freescale Semiconductor, Inc.
 * Copyright 2021 NXP
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * 1. Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 *
 * 2. Redistributions in binary form must reproduce the above copyright notice,
 * this list of conditions and the following disclaimer in the documentation
 * and/or other materials provided with the distribution.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
 * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
 * ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE
 * LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
 * CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
 * <PERSON><PERSON><PERSON><PERSON>TU<PERSON> GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
 * INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
 * CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
 * ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 */

#ifndef _IMX_REGS_H_
#define _IMX_REGS_H_

#define MACH_IMX8ULP

#define PLATFORM_USE_BGRA

#define GIC_BASE_PHY  0x2D400000
#ifdef ARCH_ARM64
#define GIC_BASE_VIRT 0xFFFFFFFF2D400000
#else
#define GIC_BASE_VIRT 0x4D400000
#endif

#define CONFIG_LPUART0_BASE 0x2809A000
#define CONFIG_LPUART1_BASE 0x2809B000
#define CONFIG_LPUART2_BASE 0x2810B000
#define CONFIG_LPUART3_BASE 0x2810C000
#define CONFIG_LPUART4_BASE 0x29390000
#define CONFIG_LPUART5_BASE 0x293A0000
#define CONFIG_LPUART6_BASE 0x29860000
#define CONFIG_LPUART7_BASE 0x29870000

#define CONFIG_CONSOLE_TTY_BASE  CONFIG_LPUART5_BASE

#ifdef ARCH_ARM64
#define CONFIG_CONSOLE_TTY_VIRT (0xFFFFFFFF00000000 + CONFIG_CONSOLE_TTY_BASE)
#else
#define CONFIG_CONSOLE_TTY_VIRT (0x20000000+ CONFIG_CONSOLE_TTY_BASE)
#endif

#define CAAM_PHY_BASE_ADDR 0x292E0000
#ifdef ARCH_ARM64
#define CAAM_BASE_ADDR  (0xFFFFFFFF00000000 + CAAM_PHY_BASE_ADDR)
#else
#define CAAM_BASE_ADDR  (0x20000000 + CAAM_PHY_BASE_ADDR)
#endif
#define CAAM_REG_SIZE 0x10000

#define CAAM_PHY_ARB_BASE_ADDR 0x26000000
#ifdef ARCH_ARM64
#define CAAM_ARB_BASE_ADDR (0xFFFFFFFF00000000 + CAAM_PHY_ARB_BASE_ADDR)
#else
#define CAAM_ARB_BASE_ADDR (0x20000000 + 0x00100000)
#endif
#define CAAM_SEC_RAM_SIZE 0x8000

#define SOC_REGS_PHY 0x20000000
#ifdef ARCH_ARM64
#define SOC_REGS_VIRT 0xFFFFFFFF20000000
#else
#define SOC_REGS_VIRT (0x20000000 + 0x20000000)
#endif
#define SOC_REGS_SIZE 0x1E000000

/* Registers for GIC */
#define MAX_INT 1020
#define GICBASE(b) (GIC_BASE_VIRT)

#define GICC_SIZE (0x1000)
#define GICD_SIZE (0x10000)
#define GICR_OFFSET (0x40000)

#define GICC_OFFSET (0x1000)
#define GICD_OFFSET (0x0000)

#define GICC_BASE_VIRT (GIC_BASE_VIRT + GICC_OFFSET)
#define GICD_BASE_VIRT (GIC_BASE_VIRT + GICD_OFFSET)

#define GIC_REG_SIZE 0x2000

#define SECURE_RAM_START_ADDR_PHY CAAM_PHY_ARB_BASE_ADDR
#define SECURE_RAM_START_ADDR CAAM_ARB_BASE_ADDR

#define XRDC_BASE_VIRT	0xFFFFFFFF292f0000

#endif
