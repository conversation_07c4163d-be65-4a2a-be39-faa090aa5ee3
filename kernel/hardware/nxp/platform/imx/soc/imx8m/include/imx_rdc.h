/*
 * Copyright 2023 NXP
 *
 */

#ifndef _IMX_RDC_H
#define _IMX_RDC_H

/* RDC MDA index */
enum rdc_mda_idx {
    RDC_MDA_A53 = 0,
    RDC_MDA_M4 = 1,
    RDC_MDA_PCIE_CTRL1 = 2,
    R<PERSON>_MDA_PCIE_CTRL2 = 3,
    RDC_MDA_VPU_DEC = 4,
    RDC_MDA_LCDIF = 5,
    RDC_MDA_CSI1 = 6,
    RDC_MDA_CSI2 = 7,
    RDC_MDA_Coresight = 8,
    RDC_MDA_DAP = 9,
    RDC_MDA_CAAM = 10,
    RDC_MDA_SDMAp = 11,
    RDC_MDA_SDMAb = 12,
    RDC_MDA_APBHDMA = 13,
    RDC_MDA_RAWNAND = 14,
    RDC_MDA_uSDHC1 = 15,
    RDC_MDA_uSDHC2 = 16,
    RDC_MDA_DCSS = 17,
    RDC_MDA_GPU = 18,
    RDC_MDA_USB1 = 19,
    RDC_MDA_USB2 = 20,
    RDC_MDA_TESTPORT = 21,
    RDC_MDA_ENET1_TX = 22,
    RDC_MDA_ENET1_RX = 23,
    RDC_MDA_SDMA2 = 24,
    RDC_MDA_SDMA1 = 26,
};

enum csu_csl_idx {
	CSU_CSL_GPIO1 = 0,
	CSU_CSL_GPIO2 = 1,
	CSU_CSL_GPIO3 = 2,
	CSU_CSL_GPIO4 = 3,
	CSU_CSL_GPIO5 = 4,
	CSU_CSL_ANA_TSENSOR = 6,
	CSU_CSL_ANA_OSC = 7,
	CSU_CSL_WDOG1 = 8,
	CSU_CSL_WDOG2 = 9,
	CSU_CSL_WDOG3 = 10,
	CSU_CSL_SDMA2 = 12,
	CSU_CSL_GPT1 = 13,
	CSU_CSL_GPT2 = 14,
	CSU_CSL_GPT3 = 15,
	CSU_CSL_ROMCP = 17,
	CSU_CSL_LCDIF = 18,
	CSU_CSL_IOMUXC = 19,
	CSU_CSL_IOMUXC_GPR = 20,
	CSU_CSL_OCOTP_CTRL = 21,
	CSU_CSL_ANATOP_PLL = 22,
	CSU_CSL_SNVS_HP = 23,
	CSU_CSL_CCM = 24,
	CSU_CSL_SRC = 25,
	CSU_CSL_GPC = 26,
	CSU_CSL_SEMAPHORE1 = 27,
	CSU_CSL_SEMAPHORE2 = 28,
	CSU_CSL_RDC = 29,
	CSU_CSL_CSU = 30,
	CSU_CSL_MST0 = 32,
	CSU_CSL_MST1 = 33,
	CSU_CSL_MST2 = 34,
	CSU_CSL_MST3 = 35,
	CSU_CSL_HDMI_SEC = 36,
	CSU_CSL_PWM1 = 38,
	CSU_CSL_PWM2 = 39,
	CSU_CSL_PWM3 = 40,
	CSU_CSL_PWM4 = 41,
	CSU_CSL_SysCounter_RD = 42,
	CSU_CSL_SysCounter_CMP = 43,
	CSU_CSL_SysCounter_CTRL = 44,
	CSU_CSL_HDMI_CTRL = 45,
	CSU_CSL_GPT6 = 46,
	CSU_CSL_GPT5 = 47,
	CSU_CSL_GPT4 = 48,
	CSU_CSL_TZASC = 56,
	CSU_CSL_MTR = 59,
	CSU_CSL_PERFMON1 = 60,
	CSU_CSL_PERFMON2 = 61,
	CSU_CSL_PLATFORM_CTRL = 62,
	CSU_CSL_QoSC = 63,
	CSU_CSL_MIPI_PHY = 64,
	CSU_CSL_MIPI_DSI = 65,
	CSU_CSL_I2C1 = 66,
	CSU_CSL_I2C2 = 67,
	CSU_CSL_I2C3 = 68,
	CSU_CSL_I2C4 = 69,
	CSU_CSL_UART4 = 70,
	CSU_CSL_MIPI_CSI1 = 71,
	CSU_CSL_MIPI_CSI_PHY1 = 72,
	CSU_CSL_CSI1 = 73,
	CSU_CSL_MU_A = 74,
	CSU_CSL_MU_B = 75,
	CSU_CSL_SEMAPHORE_HS = 76,
	CSU_CSL_SAI1 = 78,
	CSU_CSL_SAI6 = 80,
	CSU_CSL_SAI5 = 81,
	CSU_CSL_SAI4 = 82,
	CSU_CSL_USDHC1 = 84,
	CSU_CSL_USDHC2 = 85,
	CSU_CSL_MIPI_CSI2 = 86,
	CSU_CSL_MIPI_CSI_PHY2 = 87,
	CSU_CSL_CSI2 = 88,
	CSU_CSL_QSPI = 91,
	CSU_CSL_SDMA1 = 93,
	CSU_CSL_ENET1 = 94,
	CSU_CSL_SPDIF1 = 97,
	CSU_CSL_ECSPI1 = 98,
	CSU_CSL_ECSPI2 = 99,
	CSU_CSL_ECSPI3 = 100,
	CSU_CSL_UART1 = 102,
	CSU_CSL_UART3 = 104,
	CSU_CSL_UART2 = 105,
	CSU_CSL_SPDIF2 = 106,
	CSU_CSL_SAI2 = 107,
	CSU_CSL_SAI3 = 108,
	CSU_CSL_SPBA1 = 111,
	CSU_CSL_MOD_EN3 = 112,
	CSU_CSL_MOD_EN0 = 113,
	CSU_CSL_CAAM = 114,
	CSU_CSL_DDRC_SEC = 115,
	CSU_CSL_GIC_EXSC = 116,
	CSU_CSL_USB_EXSC = 117,
	CSU_CSL_OCRAM = 118,
	CSU_CSL_OCRAM_S = 119,
	CSU_CSL_VPU_SEC = 120,
	CSU_CSL_DAP_EXSC = 121,
	CSU_CSL_ROMCP_SEC = 122,
	CSU_CSL_APBHDMA_SEC = 123,
	CSU_CSL_M4_SEC = 124,
	CSU_CSL_QSPI_SEC = 125,
	CSU_CSL_GPU_EXSC = 126,
	CSU_CSL_PCIE = 127,
};

#endif
