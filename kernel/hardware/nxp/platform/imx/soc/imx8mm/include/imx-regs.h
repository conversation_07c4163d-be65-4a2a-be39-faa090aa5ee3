/*
 * Copyright (C) 2016 Freescale Semiconductor, Inc.
 * Copyright 2018 NXP
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * 1. Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 *
 * 2. Redistributions in binary form must reproduce the above copyright notice,
 * this list of conditions and the following disclaimer in the documentation
 * and/or other materials provided with the distribution.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
 * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
 * ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE
 * LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
 * CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
 * <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
 * INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
 * CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
 * ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 */

#ifndef _IMX_REGS_H_
#define _IMX_REGS_H_

#define GIC_BASE_PHY  0x38800000
#ifdef ARCH_ARM64
#define GIC_BASE_VIRT 0xFFFFFFFF38800000
#else
#define GIC_BASE_VIRT 0x58800000
#endif

#define CONFIG_UART1_BASE 0x30860000
#define CONFIG_UART2_BASE 0x30890000
#define CONFIG_UART3_BASE 0x30880000
#define CONFIG_UART4_BASE 0x30A60000

#define CONFIG_CONSOLE_TTY_BASE CONFIG_UART2_BASE

#define CONFIG_CONSOLE_TTY_VIRT (0xFFFFFFFF00000000 + CONFIG_CONSOLE_TTY_BASE)
#define MACH_IMX8MM

#define CAAM_PHY_BASE_ADDR 0x30900000
#ifdef ARCH_ARM64
#define CAAM_BASE_ADDR  (0xFFFFFFFF00000000 + CAAM_PHY_BASE_ADDR)
#else
#define CAAM_BASE_ADDR  (0x20000000 + CAAM_PHY_BASE_ADDR)
#endif
#define CAAM_PHY_ARB_BASE_ADDR 0x00100000
#ifdef ARCH_ARM64
#define CAAM_ARB_BASE_ADDR (0xFFFFFFFF00000000 + 0x40100000)
#else
#define CAAM_ARB_BASE_ADDR (0x20000000 + 0x00100000)
#endif

#define SNVS_VIRT_BASE 0xFFFFFFFF30370000

#define CCM_PHY_BASE_ADDR 0x30380000
#define CCM_REG_SIZE 0x10000

#define CAAM_REG_SIZE 0x40000

#define CCM_BASE_ADDR 0x30380000
#define CCM_BASE_ADDR_VIRT (0x40000000 + CCM_BASE_ADDR)
#define CAAM_SEC_RAM_SIZE 0x8000

/* Registers for GIC */
#define MAX_INT 160
#define GICBASE(b) (GIC_BASE_VIRT)

#define GICC_SIZE (0x1000)
#define GICD_SIZE (0x100)
#define GICR_OFFSET (0x80000)

#define SOC_REGS_PHY 0x20000000
#ifdef ARCH_ARM64
#define SOC_REGS_VIRT 0xFFFFFFFF20000000
#else
#define SOC_REGS_VIRT (0x20000000 + 0x20000000)
#endif

#define SOC_REGS_SIZE 0x1E000000

#define GICC_OFFSET (0x1000)
#define GICD_OFFSET (0x0000)

#define GICC_BASE_VIRT (GIC_BASE_VIRT + GICC_OFFSET)
#define GICD_BASE_VIRT (GIC_BASE_VIRT + GICD_OFFSET)

#define GIC_REG_SIZE 0x2000

#define SECURE_RAM_START_ADDR_PHY CAAM_PHY_ARB_BASE_ADDR
#define SECURE_RAM_START_ADDR CAAM_ARB_BASE_ADDR
#define SECURE_RAM_SIZE 0x4000

#define LCDIF_BASE_VIRT 0xFFFFFFFF32E00000


#define CSU_BASE_VIRT 0xFFFFFFFF303E0000
#define CSU_SA_LCDIF_ID 18
#define CSU_CSL_LCDIF_ID 32

#define CSL_DEFAULT      0xbb /* Secure USER/SPRV RD/WR + Non-secure SPRV RD/WR */
#define CSL_SECURE_ONLY  0x3b /* Secure USER/SPRV RD/WR + Non-secure SPRV RD*/

#endif
