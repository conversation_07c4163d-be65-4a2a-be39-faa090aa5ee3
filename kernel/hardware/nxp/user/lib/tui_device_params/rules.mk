# Copyright (C) 2019 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

LOCAL_DIR := $(GET_LOCAL_DIR)

MODULE := $(LOCAL_DIR)

LIBTEEUI_ROOT := $(TRUSTY_TOP)/system/teeui/libteeui

MODULE_SRCS += \
        $(LOCAL_DIR)/device_parameters.cpp \

MODULE_EXPORT_INCLUDES += $(LOCAL_DIR)/include

MODULE_INCLUDES += \
        $(LIBTEEUI_ROOT)/include \
        $(LOCAL_DIR)/include \
        $(TRUSTY_TOP)/trusty/user/app/confirmationui/examples/layouts/include \
        $(TRUSTY_TOP)/trusty/user/app/confirmationui/include \
        $(TRUSTY_TOP)/trusty/user/app/confirmationui/src \
        trusty/hardware/nxp/platform/imx/soc/$(PLATFORM_SOC)/include

MODULE_LIBRARY_DEPS += \
        trusty/user/base/lib/teeui-stub \
        trusty/user/base/lib/libstdc++-trusty \

include make/library.mk
