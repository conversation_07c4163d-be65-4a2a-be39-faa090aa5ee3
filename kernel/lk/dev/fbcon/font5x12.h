/*
 * Copyright (C) 2008 The Android Open Source Project
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 *  * Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *  * Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in
 *    the documentation and/or other materials provided with the
 *    distribution.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
 * "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
 * LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS
 * FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE
 * COPYRIGHT OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT,
 * INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING,
 * BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS
 * OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED
 * AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY,
 * OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT
 * OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
 * SUCH DAMAGE.
 */

unsigned font5x12[] = {
    0x00000000, 0x00000000,
    0x08421080, 0x00020084,
    0x00052940, 0x00000000,
    0x15f52800, 0x0000295f,
    0x1c52f880, 0x00023e94,
    0x08855640, 0x0004d542,
    0x04528800, 0x000b2725,
    0x00021080, 0x00000000,
    0x04211088, 0x00821042,
    0x10841082, 0x00221108,
    0x09575480, 0x00000000,
    0x3e420000, 0x00000084,
    0x00000000, 0x00223000,
    0x3e000000, 0x00000000,
    0x00000000, 0x00471000,
    0x08844200, 0x00008442,
    0x2318a880, 0x00022a31,
    0x08429880, 0x000f9084,
    0x1108c5c0, 0x000f8444,
    0x1c4443e0, 0x00074610,
    0x14a62100, 0x000423e9,
    0x26d087e0, 0x00074610,
    0x1e10c5c0, 0x00074631,
    0x088443e0, 0x00010844,
    0x1d18c5c0, 0x00074631,
    0x3d18c5c0, 0x00074610,
    0x08e20000, 0x00471000,
    0x08e20000, 0x00223000,
    0x02222200, 0x00082082,
    0x01f00000, 0x000003e0,
    0x20820820, 0x00008888,
    0x1108c5c0, 0x00020084,
    0x2b98c5c0, 0x000f05b5,
    0x2318a880, 0x0008c63f,
    0x1d2949e0, 0x0007ca52,
    0x0210c5c0, 0x00074421,
    0x252949e0, 0x0007ca52,
    0x1e1087e0, 0x000f8421,
    0x1e1087e0, 0x00008421,
    0x0210c5c0, 0x00074639,
    0x3f18c620, 0x0008c631,
    0x084211c0, 0x00071084,
    0x10842380, 0x00032508,
    0x0654c620, 0x0008c525,
    0x02108420, 0x000f8421,
    0x2b5dc620, 0x0008c631,
    0x2b59ce20, 0x0008c739,
    0x2318c5c0, 0x00074631,
    0x1f18c5e0, 0x00008421,
    0x2318c5c0, 0x01075631,
    0x1f18c5e0, 0x0008c525,
    0x1c10c5c0, 0x00074610,
    0x084213e0, 0x00021084,
    0x2318c620, 0x00074631,
    0x1518c620, 0x0002114a,
    0x2b18c620, 0x000556b5,
    0x08a54620, 0x0008c54a,
    0x08a54620, 0x00021084,
    0x088443e0, 0x000f8442,
    0x0421084e, 0x00e10842,
    0x08210420, 0x00084108,
    0x1084210e, 0x00e42108,
    0x0008a880, 0x00000000,
    0x00000000, 0x01f00000,
    0x00000104, 0x00000000,
    0x20e00000, 0x000b663e,
    0x22f08420, 0x0007c631,
    0x22e00000, 0x00074421,
    0x23e84200, 0x000f4631,
    0x22e00000, 0x0007443f,
    0x1e214980, 0x00010842,
    0x22e00000, 0x1d187a31,
    0x26d08420, 0x0008c631,
    0x08601000, 0x00071084,
    0x10c02000, 0x0c94a108,
    0x0a908420, 0x0008a4a3,
    0x084210c0, 0x00071084,
    0x2ab00000, 0x0008d6b5,
    0x26d00000, 0x0008c631,
    0x22e00000, 0x00074631,
    0x22f00000, 0x0210be31,
    0x23e00000, 0x21087a31,
    0x26d00000, 0x00008421,
    0x22e00000, 0x00074506,
    0x04f10800, 0x00064842,
    0x23100000, 0x000b6631,
    0x23100000, 0x00022951,
    0x23100000, 0x000556b5,
    0x15100000, 0x0008a884,
    0x23100000, 0x1d185b31,
    0x11f00000, 0x000f8444,
    0x06421098, 0x01821084,
    0x08421080, 0x00021084,
    0x30421083, 0x00321084,
    0x0004d640, 0x00000000,
    0x00000000, 0x00000000,
};
