#!/usr/bin/env python

import sys
import re

hexrule = re.compile("([0-9a-fA-F]+)")
hex2byterule = re.compile("([0-9a-fA-F]{4})")
hexcolonrule = re.compile("([0-9a-fA-F]+)\:")
symbolrule = re.compile("<([\._a-zA-Z]+[\._0-9a-zA-Z]*)>:")
insrule = re.compile("([a-zA-Z][\.a-zA-Z]*)")

currsymbol = ""
curraddress = 0
count = 0

for line in sys.stdin:
    t = line.split()

    if len(t) == 0:
        continue

    try:

        # match the address
        match = hexcolonrule.match(t[0])
        if match:
            #print("%s %s" % (match, match.group(1)))
            curraddress = int(match.group(1), 16)
            #print("curraddress 0x%x" % curraddress)

        # see if this is a symbol declaration
        match = symbolrule.match(t[1])
        if match:
            # print(the previous count)
            if count > 0:
                print("%d %s" % (count, currsymbol))
            count = 0

            #print("%s %s" % (match, match.group(1)))
            currsymbol = str(match.group(1))
            #print("current symbol is now '%s'" % currsymbol)
            continue

        # see if it's a one or two byte opcode
        iindex = 2
        match = hex2byterule.match(t[1])
        if not match:
            continue
        match = hex2byterule.match(t[2])
        if match:
            #print("match %s, %s" % (match, match.group(0)))
            iindex = 3

        #print("instruction starts at index %d: '%s'" % (iindex, t[iindex]))

        # match the instruction string
        insmatch = insrule.match(t[iindex])
        if not insmatch:
            continue
        ins = insmatch.group(1)
        #print("instruction '%s'" % ins)

        # look for a few special instructions
        if ins == "push":
            c = (len(t) - 1 - iindex) * 4
            #print("%d bytes pushed" % c)
            count += c

        # look for a few special instructions
        if ins == "stmdb":
            c = (len(t) - 2 - iindex) * 4
            #print("%d bytes stmed" % c)
            count += c

        if ins == "sub":
            reg = t[iindex+1]
            if reg == "sp,":
                conststr = t[iindex+2]
                c = int(conststr[1:])
                #print("subtracting from sp, val %d" % c)
                count += c

    except IndexError:
        continue
    except Exception as e:
        print("Exception %s" % e)
        continue

# print(the last count)
if count > 0:
    print("%d %s" % (count, currsymbol))

